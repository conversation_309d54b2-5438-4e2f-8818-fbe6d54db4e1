﻿// ReSharper disable NonReadonlyMemberInGetHashCode

using System;
using System.Collections.Generic;

using Apq.ChangeBubbling;
using Apq.Props;
using Apq.Utils;

using Newtonsoft.Json;

using X.PB;

namespace Props
{
	/// <summary>
	/// 通用属性行(pb)的装饰器
	/// </summary>
	public class CommonPropPbWrap : PropValue, IEquatable<CommonPropPbWrap>
	{
		protected CommonPropCfg.Types.CSVRow RowData_m = new();
		/// <summary>
		/// 属性的原始数据
		/// </summary>
		[JsonIgnore]
		public CommonPropCfg.Types.CSVRow RowData
		{
			get => RowData_m;
			set
			{
				RowData_m = value;
				LongValues.AddRange(value.LongValues);
				DoubleValues.AddRange(value.DoubleValues);
				StrValues.AddRange(value.StrValues);
			}
		}

		public CommonPropPbWrap(object key = default, IBubbleNode parent = null)
			: base(key, parent)
		{
		}

		/// <summary>
		/// 设置原始数据(pb)(存入的是rowData的克隆体)
		/// </summary>
		public CommonPropPbWrap SetRowData(CommonPropCfg.Types.CSVRow rowData)
		{
			RowData = rowData.Clone();
			return this;
		}

		#region 原始数据的属性封装(装饰模式)

		/// <summary>
		/// 行号
		/// </summary>
		public int Id
		{
			get => RowData.Id;
			set => RowData.Id = value;
		}

		/// <summary>
		/// 名称
		/// </summary>
		public string Name
		{
			get => RowData.Name;
			set => RowData.Name = value;
		}
		/// <summary>
		/// 说明
		/// </summary>
		public string Remark
		{
			get => RowData.Remark;
			set => RowData.Remark = value;
		}
		/// <summary>
		/// 图标
		/// </summary>
		public string Icon
		{
			get => RowData.Icon;
			set => RowData.Icon = value;
		}
		/// <summary>
		/// 属性类别(固有、随机)
		/// </summary>
		public PropCatalog PropCatalog
		{
			get => RowData.PropCatalog;
			set => RowData.PropCatalog = value;
		}
		/// <summary>
		/// 属性类型
		/// </summary>
		public PropType PropType
		{
			get => RowData.PropType;
			set => RowData.PropType = value;
		}
		/// <summary>
		/// 值的数据类型
		/// </summary>
		public CsValueType ValueType
		{
			get => RowData.ValueType;
			set => RowData.ValueType = value;
		}

		/// <summary>
		/// 被附者的类型
		/// </summary>
		public AttachedType AttachedType
		{
			get => RowData.AttachedType;
			set => RowData.AttachedType = value;
		}
		/// <summary>
		/// 被附者(键值列表)
		/// </summary>
		public IList<int> AttachTo
		{
			get => RowData.AttachTo;
			set
			{
				RowData.AttachTo.Clear();
				RowData.AttachTo.AddRange(value);
			}
		}
		/// <summary>
		/// 附着前提:最低武器等级(附着到枪上时)
		/// </summary>
		public int AttachPremissMinWeaponLvl
		{
			get => RowData.AttachPremissMinWeaponLvl;
			set => RowData.AttachPremissMinWeaponLvl = value;
		}
		/// <summary>
		/// 附着前提:最低枪械等级(附着到枪上时)
		/// </summary>
		public int AttachPremissMinGunLvl
		{
			get => RowData.AttachPremissMinGunLvl;
			set => RowData.AttachPremissMinGunLvl = value;
		}
		/// <summary>
		/// 附着前提:武器的最低升星经验
		/// </summary>
		public int AttachPremissMinStarExp
		{
			get => RowData.AttachPremissMinStarExp;
			set => RowData.AttachPremissMinStarExp = value;
		}
		/// <summary>
		/// 附着前提:拥有此物品Id的装备(且升星经验达到下一列指定的值)（不一定是武器）
		/// </summary>
		public int AttachPremiseGoodsID
		{
			get => RowData.AttachPremiseGoodsID;
			set => RowData.AttachPremiseGoodsID = value;
		}
		/// <summary>
		/// 附着前提:装备的最低升星经验
		/// </summary>
		public int AttachPremiseMinStarExp
		{
			get => RowData.AttachPremiseMinStarExp;
			set => RowData.AttachPremiseMinStarExp = value;
		}

		/// <summary>
		/// 应用目标类型
		/// </summary>
		public ApplyType ApplyType
		{
			get => RowData.ApplyType;
			set => RowData.ApplyType = value;
		}
		/// <summary>
		/// 应用目标键值(列表)
		/// </summary>
		public IList<int> ApplyTo
		{
			get => RowData.ApplyTo;
			set
			{
				RowData.ApplyTo.Clear();
				RowData.ApplyTo.AddRange(value);
			}
		}
		/// <summary>
		/// 应用前提:最低武器等级(应用到枪上时)/最低Buff等级(应用到Buff上时)
		/// </summary>
		public int ApplyPremiseMinWeaponLvl
		{
			get => RowData.ApplyPremiseMinWeaponLvl;
			set => RowData.ApplyPremiseMinWeaponLvl = value;
		}
		/// <summary>
		/// 应用前提:最低枪械等级(应用到枪上时)
		/// </summary>
		public int ApplyPremiseMinGunLvl
		{
			get => RowData.ApplyPremiseMinGunLvl;
			set => RowData.ApplyPremiseMinGunLvl = value;
		}
		/// <summary>
		/// 应用前提:拥有此物品Id的装备(且升星经验达到下一列指定的值)（不一定是武器）
		/// </summary>
		public int ApplyPremiseGoodsID
		{
			get => RowData.ApplyPremiseGoodsID;
			set => RowData.ApplyPremiseGoodsID = value;
		}
		/// <summary>
		/// 应用前提:装备的最低升星经验
		/// </summary>
		public int ApplyPremiseMinStarExp
		{
			get => RowData.ApplyPremiseMinStarExp;
			set => RowData.ApplyPremiseMinStarExp = value;
		}

        /// <summary>
        /// 供选前提:最低角色等级
        /// </summary>
        public int OfferPremiseMinActorLvl
        {
            get => RowData.OfferPremiseMinActorLvl;
            set => RowData.OfferPremiseMinActorLvl = value;
        }
        /// <summary>
        /// 供选前提:最低关卡等级 (关卡类型;关卡等级;...)
        /// </summary>
        public IList<int> OfferPremiseMinStageLvl
        {
            get => RowData.OfferPremiseMinStageLvl;
            set
            {
                RowData.OfferPremiseMinStageLvl.Clear();
                RowData.OfferPremiseMinStageLvl.AddRange(value);
            }
        }
        /// <summary>
        /// 供选前提:当前回合数在此列表中
        /// </summary>
        public IList<int> OfferPremiseRoundList
        {
            get => RowData.OfferPremiseRoundList;
            set
            {
                RowData.OfferPremiseRoundList.Clear();
                RowData.OfferPremiseRoundList.AddRange(value);
            }
        }
        /// <summary>
        /// 供选前提:出战了这个隐藏枪(且等级达到下两列的标准)
        /// </summary>
        public int OfferPremiseGunId
        {
            get => RowData.OfferPremiseGunId;
            set => RowData.OfferPremiseGunId = value;
        }
        /// <summary>
        /// 供选前提:最低武器等级(如果隐藏枪有对应的武器)
        /// </summary>
        public int OfferPremiseMinWeaponLvl
        {
            get => RowData.OfferPremiseMinWeaponLvl;
            set => RowData.OfferPremiseMinWeaponLvl = value;
        }
		/// <summary>
		/// 供选前提:最低枪械等级
		/// </summary>
		public int OfferPremiseMinGunLvl
		{
			get => RowData.OfferPremiseMinGunLvl;
			set => RowData.OfferPremiseMinGunLvl = value;
		}
		/// <summary>
		/// 供选前提:拥有此物品Id的装备(且升星经验达到下一列指定的值)（不一定是武器）
		/// </summary>
		public int OfferPremiseGoodsID
		{
			get => RowData.OfferPremiseGoodsID;
			set => RowData.OfferPremiseGoodsID = value;
		}
		/// <summary>
		/// 供选前提:装备的最低升星经验
		/// </summary>
		public int OfferPremiseMinStarExp
		{
			get => RowData.OfferPremiseMinStarExp;
			set => RowData.OfferPremiseMinStarExp = value;
		}
		/// <summary>
		/// 供选前提:角色已获得此Id的随机属性
		/// </summary>
		public int OfferPremisePropId
		{
			get => RowData.OfferPremisePropId;
			set => RowData.OfferPremisePropId = value;
		}
		/// <summary>
		/// 供选前提:最大获得次数
		/// </summary>
		public int MaxGotTimes
		{
			get => RowData.MaxGotTimes;
			set => RowData.MaxGotTimes = value;
		}

		/// <summary>
		/// 供选权重
		/// </summary>
		public int OfferWeight
		{
			get => RowData.OfferWeight;
			set => RowData.OfferWeight = value;
		}

		/// <summary>
		/// 提升目标的哪个属性
		/// </summary>
		public PropType HoistPropType
		{
			get => RowData.HoistPropType;
			set => RowData.HoistPropType = value;
		}
		/// <summary>
		/// 提升方法
		/// </summary>
		public PropHoistMethod HoistMethod
		{
			get => RowData.HoistMethod;
			set => RowData.HoistMethod = value;
		}
		/// <summary>
		/// 如何获取提升系数
		/// </summary>
		public HoistCoeMethod HoistCoeMethod
		{
			get => RowData.HoistCoeMethod;
			set => RowData.HoistCoeMethod = value;
		}

		/// <summary>
		/// 本属性的品质
		/// </summary>
		public int Quality
		{
			get => RowData.Quality;
			set => RowData.Quality = value;
		}
		/// <summary>
		/// 作用在武器上的属性,根据升星经验来显示说明
		/// </summary>
		public string RemarkByStartExp
		{
			get => RowData.RemarkByStartExp;
			set => RowData.RemarkByStartExp = value;
		}

		#endregion

		#region ICloneable
		public override object Clone()
		{
			var clone = new CommonPropPbWrap();
			CopyTo(clone);
			return clone;
		}

		public void CopyTo(CommonPropPbWrap other)
		{
			//// 复制前暂停更改事件(RowData)
			//var changingSuspend = other.ChangingSuspend;
			//var changedSuspend = other.ChangedSuspend;
			//other.ChangingSuspend = true;
			//other.ChangedSuspend = true;

			// 会影响基类定义的数据，必须在调用基类方法前
			other.RowData = RowData.Clone();

			//// 复制后恢复更改事件(RowData)
			//other.ChangingSuspend = changingSuspend;
			//other.ChangedSuspend = changedSuspend;

			base.CopyTo(other);
		}
		#endregion

		#region IEquatable
		public override int GetHashCode() => RowData.GetHashCode();

		public bool Equals(CommonPropPbWrap other)
		{
			return other != null && RowData.Equals(other.RowData);
		}
		public override bool Equals(object obj) => obj is CommonPropPbWrap other && Equals(other);

		public static bool operator ==(CommonPropPbWrap left, CommonPropPbWrap right)
		{
			return Util.IsEquals(left, right);
		}

		public static bool operator !=(CommonPropPbWrap left, CommonPropPbWrap right)
		{
			return !(left == right);
		}
		#endregion
	}
}
