﻿// ReSharper disable InconsistentNaming

using System.Linq;
using System.Threading;
using System.Collections.Generic;
using System.Collections;

using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using RxEventsM2V;

using Spine.Unity;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     物件基类(界面)
    /// </summary>
    public abstract class ThingBehaviour : MonoBehaviour
    {
        /// <summary>
        ///     物件(数据)
        /// </summary>
        public ThingBase Thing { get; set; }

        /// <summary>
        ///     每一帧是否将界面的状态(如：位置、朝向等)同步到数据中。回收到对象池时，应将其设为false。
        /// </summary>
        public bool SyncToThing { get; set; }

        /// <summary>
        ///     已移动时长
        /// </summary>
        public FloatReactiveProperty MoveDuration { get; } = new();

        /// <summary>
        ///     是否应该停留
        /// </summary>
        public bool ShouldStay { get; set; }

        /// <summary>
        ///     物件的血条
        /// </summary>
        public HealthBar HpBar { get; set; }

        private Transform armorSprite { get; set; }
        /// <summary>
		/// 生物的骨骼动画
		/// </summary>
		public SkeletonAnimation SkeAni { get; set; }

        // 层级锁定系统 - 无论任何其他系统如何修改，我们都会强制保持正确的值
        public static Dictionary<int, int> _correctSortingOrderCache = new Dictionary<int, int>();
        // 是否已启用渲染层级锁定机制
        private static bool _sortingOrderLockEnabled = false;
        // 是否正在执行劫持
        private static bool _interceptingSort = false;
        // 需要强制刷新的物体队列
        public static HashSet<int> _forcedRefreshQueue = new HashSet<int>();
        // 是否已经替换了渲染系统
        private static bool _rendererSystemHijacked = false;
        // 原始的SetPropertyBlock方法
        private static System.Reflection.MethodInfo _originalSetPropertyBlockMethod = null;
        // 层级控制渲染器字典
        public static Dictionary<MeshRenderer, SortingOrderController> _controllerDict = new Dictionary<MeshRenderer, SortingOrderController>();
        // 强制执行器：保存所有需要控制的渲染器及其正确的层级值
        private static Dictionary<int, RenderForcer> _renderForcers = new Dictionary<int, RenderForcer>();
        
        // 记录需要强制控制的渲染器及其正确层级值
        private class RenderForcer
        {
            public MeshRenderer Renderer;         // 渲染器引用
            public int CorrectSortingOrder;       // 正确的层级值
            public int InstanceID;                // 实例ID
            public MonoBehaviour Owner;           // 所属对象
            public float LastUpdateTime;          // 最后更新时间
            
            // 应用正确的层级值
            public void ForceCorrectOrder()
            {
                if (Renderer != null && Renderer.sortingOrder != CorrectSortingOrder)
                {
                    // 强制设置正确的层级值
                    Renderer.sortingOrder = CorrectSortingOrder;
                    
                    // 如果层级值被其他系统修改得非常离谱，记录警告
                    if (Renderer.sortingOrder != CorrectSortingOrder)
                    {
                     //   Debug.LogError($"严重错误: 无法设置层级值! 对象={Owner?.name}, ID={InstanceID}, 尝试设置={CorrectSortingOrder}, 实际值={Renderer.sortingOrder}");
                    }
                }
            }
        }

        public virtual void Awake()
        {
            SkeAni = GetComponentInChildren<SkeletonAnimation>();
            Debug.Log($"[ThingBehaviour.Awake] 对象名={gameObject.name}, 获取到SkeletonAnimation组件={SkeAni != null}, 对象路径={GetGameObjectPath(gameObject)}");
            
            if (SkeAni != null)
            {
                string animations = GetAvailableAnimations(SkeAni);
                Debug.Log($"[ThingBehaviour.Awake] 可用动画列表: {animations}");
                
                // 第一次执行时启动全局渲染层级锁定系统
                if (!_sortingOrderLockEnabled)
                {
                    StartGlobalSortingOrderLockSystem();
                }
                
                // 如果找到SkeAni，立即计算并设置初始层级
                SetSpineSortingOrderBySelfPosition();
                
                // 为SkeAni的MeshRenderer添加到强制执行器
                MeshRenderer mr = SkeAni.GetComponent<MeshRenderer>();
                if (mr != null)
                {
                    RegisterRendererForForcing(mr, gameObject.GetInstanceID(), this);
                }
            }
            
            HpBar = GetComponentInChildren<HealthBar>();
            if (HpBar)
            {
                HpBar.bar.gameObject.SetActive(false);
            }

            armorSprite = transform.Find("wuqi/Armor");
            if (!armorSprite)
            {
                armorSprite = transform.Find("wuqi(1)/Armor");
            }
        }

        public virtual void Start()
        {
            if (HpBar)
            {
                // 血量改变时显示血条
                MessageBroker.Default.Receive<ThingHp>().Where(e => Util.IsEquals(e.Thing, Thing)).Subscribe(e =>
                {
                    CTS_ShowHpBar.Cancel();
                    CTS_ShowHpBar = new CancellationTokenSource();
                    Task_ShowHpBar(e.NewValue, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault(), 3,
                        CTS_ShowHpBar.Token).Forget();
                }).AddTo(this.GetCancellationTokenOnDestroy());

                // 护甲改变时显示护罩
                MessageBroker.Default.Receive<ThingArmor>().Where(e => Util.IsEquals(e.Thing, Thing)).Subscribe(e =>
                {
                    Debug.Log($"护甲变化:{e.NewValue}/{Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault()}");
                    if (e.Inc < 0)
                    {
                        MessageBroker.Default.Publish(new ThingHp
                        {
                            Thing = e.Thing, OriginalValue = e.Thing.Hp.Value, NewValue = e.Thing.Hp.Value
                        });
                        HpBar.SetDisplayArmorBar((float)(e.NewValue /
                                                         Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault()));
                    }

                    ShowArmorSprite(e.NewValue, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                }).AddTo(this);

                // 初始护罩
                ShowArmorSprite(Thing.Armor.Value, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
            }
        }

        public virtual void Update()
        {
            // 将位置和自转朝向同步到数据中
            if (SyncToThing && Thing != null)
            {
                DoSyncToThing();
            }
        }

        protected virtual void OnDestroy()
        {
            StopRotate();
            StopMove();

            // 从强制系统中移除 (整合自CleanupRegistrations方法)
            int instanceID = gameObject.GetInstanceID();
            if (_renderForcers.ContainsKey(instanceID))
            {
                _renderForcers.Remove(instanceID);
            }
            
            // 如果这是最后一个对象，清理全局事件
            if (_renderForcers.Count == 0 && _sortingOrderLockEnabled)
            {
                Camera.onPreRender -= OnPreRenderForceSortingOrder;
                _sortingOrderLockEnabled = false;
            }

            // Thing?.Dispose();
        }

        /// <summary>
        ///     对界面进行初始化
        /// </summary>
        public async UniTask Init()
        {
            // 目的:等到Awake执行后
            await UniTask.NextFrame();

            await OnBeforeInitView();

            await OnAfterInitView();
        }

        /// <summary>
        ///     初始化界面前
        /// </summary>
        public virtual async UniTask OnBeforeInitView()
        {
            if (Thing is { Provider_BeforeInitView: not null })
            {
                UniTask task = UniTask.Defer(() =>
                {
                    Thing.Provider_BeforeInitView?.Invoke(this);
                    return UniTask.CompletedTask;
                });
                await task;
                await UniTask.NextFrame();
            }
        }

        /// <summary>
        ///     初始化界面后
        /// </summary>
        public virtual async UniTask OnAfterInitView()
        {
            if (Thing is { Provider_AfterInitView: not null })
            {
                UniTask task = UniTask.Defer(() =>
                {
                    Thing.Provider_AfterInitView?.Invoke(this);
                    return UniTask.CompletedTask;
                });
                await task;
            }
        }

        /// <summary>
        ///     将一些界面的值同步至数据中
        /// </summary>
        public virtual void DoSyncToThing()
        {
            if (Thing != null)
            {
                // 记录同步前的位置，用于调试
                Vector3 oldDataPosition = Thing.Position;
                Vector3 currentTransformPosition = transform.position;
                
                // 位置
                Thing.Position = transform.position;
                // 自转朝向
                Thing.RotationTowards.Value = transform.rotation.eulerAngles;
                
                // 如果是角色，添加位置同步日志
                if (Thing is Thing.ActorThing)
                {
                    float distance = Vector3.Distance(oldDataPosition, currentTransformPosition);
                    if (distance > 0.1f)
                    {
                        Debug.Log($"77777 角色位置同步: 数据旧位置={oldDataPosition}, Transform当前位置={currentTransformPosition}, 位置差距={distance:F3}");
                    }
                }
            }
        }

        /// <summary>
        ///     移除物件数据,并将界面回收到界面池(隐藏就是回收到池了)
        /// </summary>
        public virtual async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);

                StopRotate();
                StopMove();

                gameObject.SetActive(false);
                SyncToThing = false;
                // 回收到池中后，就不应停留了
                ShouldStay = false;

                // 延时1分钟后置为null
                //await UniTask.Delay(System.TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (System.OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        #region 移动

        /// <summary>
        ///     计算当前帧的移动方向(按HowMoveDir指定的方法计算)
        /// </summary>
        public virtual Vector3 CalcMoveDir()
        {
            Vector3 rtn = Thing.GetTotalLong(PropType.HowMoveDir).FirstOrDefault() switch
            {
                (int)DirMethod.Up => Vector3.up,
                (int)DirMethod.Down => Vector3.down,
                (int)DirMethod.Left => Vector3.left,
                (int)DirMethod.Right => Vector3.right,
                (int)DirMethod.Straight => CalcDir_Straight(),
                _ => Vector3.zero
            };
            return rtn;
        }

        /// <summary>
        ///     计算当前帧的直线移动方向
        /// </summary>
        public virtual Vector3 CalcDir_Straight()
        {
            // Thing.MoveDirection_Straight.Value = Vector3.zero;
            return Thing.MoveDirection_Straight.Value;
        }

        /// <summary>
        ///     任务的取消令牌:移动
        /// </summary>
        public CancellationTokenSource CTS_Move { get; set; } = new();

        /// <summary>
        ///     开始移动(按数据中设定的移动方法)
        /// </summary>
        /// <returns>是否已开始移动</returns>
        public virtual void StartMove()
        {
            StopMove();
            CTS_Move = new CancellationTokenSource();

            MoveDuration.Value = 0;
            DoTask_Move(CTS_Move.Token).Forget();
        }

        /// <summary>
        ///     停止移动
        /// </summary>
        public virtual void StopMove()
        {
            CTS_Move.Cancel();
        }

        /// <summary>
        ///     任务实现:移动
        /// </summary>
        public virtual async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    MoveDuration.Value += Time.deltaTime;

                    //// 移动已被Buff接管
                    //if (Thing.IsTakeMove()) continue;

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        switch (Thing.GetTotalLong(PropType.MoveMethod).FirstOrDefault())
                        {
                            case (int)MoveMethod.PositionMove:
                                {
                                    LineSegment line = MoveOne_PositionMove(Time.deltaTime);
                                    if (OnAfterMoveOne(line))
                                    {
                                        return;
                                    }
                                }
                                break;
                            case (int)MoveMethod.RigidBodyMove:
                                {
                                    MoveOne_RigidBodyMove(Time.deltaTime);
                                }
                                break;
                        }
                    }
                    catch (System.OperationCanceledException)
                    {
                        throw;
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <summary>
        ///     移动前的判断
        /// </summary>
        /// <returns>是否不再移动</returns>
        protected virtual bool OnBeforeMoveOne()
        {
            Vector3 speed = CalcSpeed_PositionMove();
            // 有速度就是要移动，则计时
            if (speed != Vector3.zero)
            {
                MoveDuration.Value += Time.deltaTime;
            }

            return false;
        }

        /// <summary>
        ///     移动后的处理
        /// </summary>
        /// <returns>是否不再移动</returns>
        protected virtual bool OnAfterMoveOne(LineSegment line)
        {
            return false;
        }

        /// <summary>
        ///     移动结束时干什么
        /// </summary>
        protected virtual void OnMoveEnd()
        {
        }

        #region PositionMove

        /// <summary>
        ///     计算物件的当前速度(方向和大小)
        /// </summary>
        protected virtual Vector3 CalcSpeed_PositionMove()
        {
            float speed = ShouldStay ? 0
                : Thing.IsHitBack ? Thing.HitBackSpeed
                : (float)Thing.GetTotalDouble(PropType.Speed).FirstOrDefault();
            // 速度不能是负值
            if (speed <= float.Epsilon)
            {
                speed = 0;
            }

            // 停留状态的方向取0
            Vector3 dir_1 = speed == 0 ? Vector3.zero : CalcMoveDir();

            // 击退状态时速度取反
            if (Thing.IsHitBack)
            {
                dir_1 = -1 * dir_1;
            }

            // 冰冻状态速度取0
            if (Thing.IsFreeze)
            {
                speed = 0;
            }

            return dir_1 * speed;
        }

        /// <summary>
        ///     移动一次。计算这次直线移动的线段,修改位置,并设置下一帧的移动方向
        /// </summary>
        /// <param name="duration">这次移动所用的时长(秒)</param>
        /// <returns>这次直线移动经过的线段</returns>
        public virtual LineSegment MoveOne_PositionMove(float duration)
        {
            // 起点为当前位置
            LineSegment rtn = new LineSegment { PosStart = transform.position };

            // 当前速度(方向和大小)
            Vector3 dir = CalcSpeed_PositionMove();

            // 这次移动的长度
            float distance = dir.magnitude * duration;

            // 无障碍物时的直线移动线段
            rtn.Dir = distance * dir.normalized;

            // 当前移动终点的信息
            StraightEndPoint_PositionMove rtnEndPoint = new StraightEndPoint_PositionMove { EndPoint = rtn.PosEnd };

            // 检测障碍物
            {
                // 碰到的边及其位置
                StraightEndPoint_PositionMove collideEdgePoint = DoCollideRectObstacles_PositionMove(rtn);
                if (collideEdgePoint != null)
                {
                    rtnEndPoint = collideEdgePoint;
                    rtn.Dir = collideEdgePoint.EndPoint - rtn.PosStart;
                }
            }

            // // 检测边界
            // {
            //     // 碰到的边及其位置
            //     var collideEdgePoint = DoCollideBounds_PositionMove(rtn);
            //     if (collideEdgePoint != null)
            //     {
            //         rtnEndPoint = collideEdgePoint;
            //         rtn.Dir = collideEdgePoint.EndPoint - rtn.PosStart;
            //     }
            // }

            // 传送至终点
            TranslateToEndPoint_PositionMove(rtnEndPoint);

            return rtn;
        }

        /// <summary>
        ///     执行碰撞检测:矩形障碍物。碰到不能通过的矩形障碍物时，返回新的终点信息
        /// </summary>
        public virtual StraightEndPoint_PositionMove DoCollideRectObstacles_PositionMove(LineSegment move,
            System.Func<RectObstacleThing, bool> predicate = null)
        {
            // 默认条件：有类型的矩形障碍物
            predicate ??= x => x.TerrainType > TerrainType.None;

            StraightEndPoint_PositionMove rtn = null;
            // 射线检测 找出碰到的地形
            // ReSharper disable once Unity.PreferNonAllocApi
            RaycastHit2D[] hits = Physics2D.CircleCastAll(move.PosStart, Thing.TotalProp_Radius,
                move.DirNormal, move.Dir.magnitude, LayerMask.GetMask("Terrain")
            );
            if (hits is { Length: > 0 })
            {
                // 碰到的第一个满足条件的矩形障碍物
                // ReSharper disable once IdentifierTypo
                RaycastHit2D raycastHit2D = hits.FirstOrDefault(x =>
                    x && x.collider.gameObject &&
                    x.collider.gameObject.GetComponent<RectObstacle>() is
                        { RectObstacleThing: not null } rectObstacle &&
                    (predicate == null || predicate.Invoke(rectObstacle.RectObstacleThing)));
                if (raycastHit2D)
                {
                    RectObstacleThing rectObstacleThing =
                        raycastHit2D.collider.gameObject.GetComponent<RectObstacle>().RectObstacleThing;
                    RectArea2D rectArea2D = rectObstacleThing.RectAreas2D.FirstOrDefault();
                    // 碰到矩形障碍物的哪条边
                    LineSegment edge = rectArea2D?.GetEdgeContainsPoint(raycastHit2D.point).FirstOrDefault();
                    if (edge != null)
                    {
                        rtn = new StraightEndPoint_PositionMove
                        {
                            CollidePoint = raycastHit2D.point,
                            MirrorInNormal = edge.Dir.RotateAround(Vector3.forward, 90).normalized
                        };
                    }
                }
            }

            return rtn;
        }

        // /// <summary>
        // /// 执行碰撞检测:边界。碰到不能通过的边界时，返回新的终点信息
        // /// </summary>
        // public virtual StraightEndPoint_PositionMove DoCollideBounds_PositionMove(LineSegment move)
        // {
        //     StraightEndPoint_PositionMove rtn = null;
        //     // 获取战斗区域的边
        //     var edges = GlobalMgr.GetBattleEdges();
        //     foreach (var edge in edges)
        //     {
        //         var (intersect, point) = move.TryGetIntersectPoint(edge);
        //         if (intersect)
        //         {
        //             rtn = new()
        //             {
        //                 CollidePoint = point,
        //                 MirrorInNormal = edge.Dir.RotateAround(Vector3.forward, -90).normalized,
        //             };
        //             break;
        //         }
        //     }
        //
        //     return rtn;
        // }

        /// <summary>
        ///     物件传送至终点
        /// </summary>
        public virtual void TranslateToEndPoint_PositionMove(StraightEndPoint_PositionMove endPoint)
        {
            transform.position = endPoint.EndPoint;
        }

        #endregion

        #region RigidBodyMove

        /// <summary>
        ///     刚体运动(当速度或方向改变时，设置一次刚体速度)
        /// </summary>
        /// <param name="duration">这次移动前经过的时长(秒)</param>
        public void MoveOne_RigidBodyMove(float duration)
        {
            if (!TryGetComponent(out Rigidbody2D rgBody))
            {
                return;
            }

            Vector2 oldVelocity = rgBody.velocity;

            float speed = Thing.IsHitBack
                ? Thing.HitBackSpeed
                : (float)Thing.GetTotalDouble(PropType.Speed).FirstOrDefault();
            // 速度不能是负值
            if (speed <= float.Epsilon)
            {
                speed = 0;
            }

            Vector3 dir_1 = CalcMoveDir();

            // 击退状态时速度取反
            if (Thing.IsHitBack)
            {
                dir_1 = -1 * dir_1;
            }

            // 冰冻状态速度取0
            if (Thing.IsFreeze)
            {
                speed = 0;
            }

            Vector3 newVelocity = speed * dir_1;

            if ((Vector2)newVelocity != oldVelocity)
            {
                rgBody.velocity = newVelocity;
            }

            //DOTween.To(() => rgBody.velocity, v => rgBody.velocity = v,
            //	speed * (Vector2)CalcMoveDir(), duration);
        }

        #endregion

        #endregion

        #region 自转

        /// <summary>
        ///     任务的取消令牌:自转
        /// </summary>
        public CancellationTokenSource CTS_Rotate { get; set; } = new();

        /// <summary>
        ///     开始自转
        /// </summary>
        public virtual void StartRotate()
        {
            StopRotate();
            CTS_Rotate = new CancellationTokenSource();
            DoTask_Rotate(CTS_Rotate.Token).Forget();
        }

        /// <summary>
        ///     停止自转
        /// </summary>
        public virtual void StopRotate()
        {
            CTS_Rotate.Cancel();
        }

        /// <summary>
        ///     任务实现:自转
        /// </summary>
        public virtual async UniTaskVoid DoTask_Rotate(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    try
                    {
                        RotateOne(token, Time.deltaTime);
                    }
                    catch (System.OperationCanceledException)
                    {
                        throw;
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        ///     执行一次自转
        /// </summary>
        /// <param name="token"></param>
        /// <param name="duration">这次自转前经过的时长(秒)</param>
        public virtual void RotateOne(CancellationToken token, float duration)
        {
            // 自转角速度
            float rotateSpeed = (float)Thing.GetTotalDouble(PropType.RotateSpeed).FirstOrDefault();
            if (rotateSpeed == 0)
            {
                return;
            }

            float rotateZ = transform.localRotation.eulerAngles.z + (rotateSpeed * duration);
            transform.DOLocalRotate(new Vector3(0, 0, rotateZ), duration, RotateMode.FastBeyond360);

            //transform.Rotate(Vector3.forward, rotateSpeed * duration,
            //	Space.Self);
        }

        #endregion

        #region 血条

        /// <summary>
        ///     任务的取消令牌:
        /// </summary>
        protected CancellationTokenSource CTS_ShowHpBar { get; set; } = new();

/// <summary>
/// 显示血条
/// </summary>
public virtual async UniTaskVoid Task_ShowHpBar(double value, double maxValue, float delay = 30000,
    CancellationToken token = default)
{
    try
    {
        // 设置血条长度
        HpBar.SetDisplayHealth((float)(value / maxValue));
        HpBar.bar.gameObject.SetActive(true);
                                if (value < maxValue * 0.001f  )
                                {
                                    HpBar.barOutline.gameObject.SetActive(true);
                                        Debug.Log("怪物血条长度11");
                                 }
        // 判断是否应该隐藏 barOutline
        bool shouldHideBarOutline =  (value > 0 && value < maxValue * 0.99f);
        bool isBarOutlineHidden = !HpBar.barOutline.gameObject.activeSelf; // 使用 activeSelf 检查当前是否已隐藏

        // 如果需要隐藏且当前未隐藏，则隐藏 barOutline
        if (shouldHideBarOutline && !isBarOutlineHidden)
        {
            HpBar.barOutline.gameObject.SetActive(false);
            Debug.Log("怪物血条长度：隐藏 barOutline");
            
        }
        
                bool shouldMoveBarOutline = value > maxValue * 0.1f && value < maxValue * 0.99f;
        // 等待指定的延迟时间之前先检查并可能修改 barOutline 的位置
         if (shouldMoveBarOutline)
        {
            // 假设 HpBar.barOutline 是一个具有 Transform 组件的游戏对象
            Transform barOutlineTransform = HpBar.barOutline.GetComponent<Transform>();
            if (barOutlineTransform != null)
            {
                // 修改 barOutline 的位置，假设您想要修改的是 X 坐标
                barOutlineTransform.position = new Vector3(1000, barOutlineTransform.position.y, barOutlineTransform.position.z);
                Debug.Log("怪物血条长度：移动 barOutline 到 X=1000");
            }
            else
            {
                Debug.LogError("未能获取到 HpBar.barOutline 的 Transform 组件");
            }
        }
        // 等待指定的延迟时间
        await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);

        // 检查是否请求取消
        if (token.IsCancellationRequested)
        {
            return;
        }

        // 理论上，如果 value 和 maxValue 在等待期间没有变化，这里不需要重新设置血条长度
        // 但为了安全起见，或者如果外部因素可能改变了它，可以重新设置
        HpBar.SetDisplayHealth((float)(value / maxValue));
        // 注意：这里不需要再次设置 HpBar.bar.gameObject.SetActive(true)，因为它在开始时已经设置为 true
        // 并且没有代码在期间将其设置为 false（除非在别的地方有代码这样做）

        // 同样，不需要再次改变 HpBar.barOutline.gameObject.SetActive 的状态，因为它已经在需要时被隐藏
    }
    catch (System.OperationCanceledException) { throw; }
    catch (MissingReferenceException) { }
    catch (System.Exception ex)
    {
        Debug.LogException(ex);
    }
}
        /// <summary>
        ///     显示护甲图片
        /// </summary>
        /// <param name="curArmorValue"></param>
        /// <param name="maxHp"></param>
        public void ShowArmorSprite(double curArmorValue, double maxHp)
        {
            if (armorSprite == null)
            {
                return;
            }

            if (curArmorValue < 5 + (0.1f * maxHp))
            {
                armorSprite.gameObject.SetActive(false);
                armorSprite.GetComponent<Animation>().enabled = false;
            }
            else if (curArmorValue >= 5 + (0.1f * maxHp) && curArmorValue < 5 + (0.2f * maxHp))
            {
                armorSprite.gameObject.SetActive(true);
                armorSprite.GetComponent<Animation>().enabled = true;
            }
            else if (curArmorValue >= 5 + (0.2f * maxHp))
            {
                armorSprite.gameObject.SetActive(true);
                armorSprite.GetComponent<Animation>().enabled = false;
            }
        }

        #endregion
        /// <summary>
		/// 播放动画
		/// </summary>
		/// <param name="aniName">动画名字</param>
		/// <param name="loop">是否循环</param>
		/// <param name="isBackToIdle">当前动画结束是否要回到idle状态</param>
		/// <param name="timeScale">动画播放速度倍数，默认为1</param>
		public virtual void PlayAnimation(string aniName, bool loop = false, bool isBackToIdle = true, float timeScale = 1f)
        {
            if (SkeAni == null) 
            {
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
                
                if (SkeAni != null)
                {
                    // 记录获取到的SkeAni信息供调试用
                }
            }
            
            if (SkeAni == null || SkeAni.state == null) 
            {
                Debug.LogWarning($"无法播放动画 {aniName}: 找不到骨骼动画组件或状态为空, 对象路径={GetGameObjectPath(gameObject)}");
                return;
            }
            
            // 检查当前是否已经在播放该动画
            var currentTrackEntry = SkeAni.state.GetCurrent(0);
            string currentAnim = currentTrackEntry?.Animation?.Name;
            
            // 如果当前动画与目标动画相同，并且循环状态也相同，则不执行任何操作
            if (currentAnim == aniName && currentTrackEntry != null && currentTrackEntry.Loop == loop)
            {
                return;
            }
            
            // 检查是否存在该动画
            var animationToPlay = SkeAni.skeleton.Data.FindAnimation(aniName);
            if (animationToPlay == null)
            {
                Debug.LogWarning($"动画 {aniName} 不存在于 {gameObject.name} ({GetType().Name})。可用动画: {GetAvailableAnimations(SkeAni)}");
                // 尝试播放默认动画，例如 idle
                var idleAnimation = SkeAni.skeleton.Data.FindAnimation("idle01") ?? SkeAni.skeleton.Data.FindAnimation("idle");
                if (idleAnimation != null)
                {
                    SkeAni.state.SetAnimation(0, idleAnimation.Name, true); 
                }
                else
                {
                    Debug.LogError($"无法找到动画 {aniName} 且无默认idle动画可播放。");
                }
                return;
            }

            // 特殊处理attack01动画，确保其优先级
            bool isAttackAnimation = aniName == "attack01";
            if (isAttackAnimation)
            {
                // 清空当前轨道上的动画
                SkeAni.state.ClearTrack(0);
            }
            
            SkeAni.timeScale = timeScale;
            var trackEntry = SkeAni.state.SetAnimation(0, aniName, loop);

            if (trackEntry != null)
            {
                // 特殊处理attack01动画的参数
                if (isAttackAnimation)
                {
                    trackEntry.MixDuration = 0.1f; // 减少混合时间，更快切换到新动画
                }
                
                trackEntry.Complete += entry => {
                    // 动画播放完成的回调
                };

                if (!loop && isBackToIdle)
                {
                    var idleAnimName = "idle01"; // 或者其他默认的idle动画名
                    if (SkeAni.skeleton.Data.FindAnimation(idleAnimName) == null) {
                        idleAnimName = "idle"; // 备用idle动画名
                    }
                    
                    if (SkeAni.skeleton.Data.FindAnimation(idleAnimName) != null) 
                    {
                        SkeAni.state.AddAnimation(0, idleAnimName, true, 0);
                    }
                    else
                    {
                        Debug.LogWarning($"找不到idle动画 ({idleAnimName} 或 idle) 来在 {aniName} 之后播放。");
                    }
                }
            }
            
            // 验证播放是否成功
            var verifyCurrentTrack = SkeAni.state.GetCurrent(0);
            if (verifyCurrentTrack != null && verifyCurrentTrack.Animation != null)
            {
                // 如果attack01动画未成功设置，再次尝试
                if (isAttackAnimation && verifyCurrentTrack.Animation.Name != "attack01")
                {
                    Debug.LogWarning($"attack01动画未成功设置，再次尝试");
                    SkeAni.state.ClearTrack(0);
                    SkeAni.state.SetAnimation(0, "attack01", loop);
                }
            }
        }

        // 辅助方法：获取GameObject的完整路径
        private string GetGameObjectPath(GameObject obj)
        {
            if (obj == null) return "null";
            string path = obj.name;
            Transform parent = obj.transform.parent;
            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }
            return path;
        }
        
        // 辅助方法：获取可用的动画列表
        private string GetAvailableAnimations(SkeletonAnimation skeAni)
        {
            if (skeAni == null || skeAni.skeleton == null || skeAni.skeleton.Data == null) 
                return "无法获取动画列表";
                
            var animations = skeAni.skeleton.Data.Animations;
            if (animations == null || animations.Count == 0) 
                return "没有动画";
                
            return string.Join(", ", animations.Select(a => a.Name));
        }

        public void SetSpineModelDirection(int value)
        {
            if (SkeAni == null) SkeAni = GetComponentInChildren<SkeletonAnimation>();
            if (SkeAni == null || SkeAni.state == null) return;
            SkeAni.skeleton.ScaleX = value;
        }

        public void SetSpineSortingOrderBySelfPosition()
        {
            try
            {
                if (SkeAni == null) SkeAni = GetComponentInChildren<SkeletonAnimation>();
                if (SkeAni == null) return;
                MeshRenderer mr = SkeAni.GetComponent<MeshRenderer>();
                if (mr == null) return;
                
                // 获取物体的唯一标识
                int instanceID = gameObject.GetInstanceID();
                
                // 记录原始值，用于日志和调试
                float originalY = transform.position.y;
                float originalX = transform.position.x;
                int oldSortingOrder = mr.sortingOrder;
                
                // 修改层级计算公式，确保结果在Unity合法范围内
                // Unity排序层级范围: -32768 到 32767
                const int MIN_SORTING_VALUE = -32768;
                const int MAX_SORTING_VALUE = 32767;
                
                // 计算Y部分 - 保持负相关性但缩小范围
                float scaleFactor = 100.0f; // 缩小因子
                int yPart = -(int)(transform.position.y * scaleFactor);
                
                // 确保Y部分不会导致溢出
                if (yPart < MIN_SORTING_VALUE / 2)
                    yPart = MIN_SORTING_VALUE / 2;
                if (yPart > MAX_SORTING_VALUE / 2)
                    yPart = MAX_SORTING_VALUE / 2;
                
                // X部分计算 - 保持个位数影响，确保相同Y坐标的物体有微小区别
                int xPart = (int)(originalX * 10) % 10;
                
                // 加上基础偏移
                int result = yPart + xPart - 100; // 使用减法确保结果更可能为负数
                
                // 确保结果在合法范围内并且为负数
                if (result >= 0)
                    result = -1000; // 使用小的负值
                
                // 最终安全检查
                if (result < MIN_SORTING_VALUE)
                    result = MIN_SORTING_VALUE;
                if (result > MAX_SORTING_VALUE)
                    result = MAX_SORTING_VALUE;
                
                // 记录调试信息
                if (oldSortingOrder != result)
                {
                  //  Debug.Log($"[层级计算] 物体={gameObject.name}, 坐标=({originalX:F2}, {originalY:F2}), " +
                      //        $"原始计算={yPart + xPart - 100}, 最终层级={result}, 旧层级={oldSortingOrder}");
                }
                
                // 更新缓存中的正确值
                _correctSortingOrderCache[instanceID] = result;
                
                // 直接设置
                mr.sortingOrder = result;
                
                // 通过强制执行器设置
                if (_renderForcers.TryGetValue(instanceID, out RenderForcer forcer))
                {
                    forcer.CorrectSortingOrder = result;
                    forcer.LastUpdateTime = Time.time;
                    forcer.ForceCorrectOrder();
                }
                else
                {
                    // 如果没有强制执行器，创建一个
                    RegisterRendererForForcing(mr, instanceID, this);
                    
                    // 更新并应用正确值
                    if (_renderForcers.TryGetValue(instanceID, out forcer))
                    {
                        forcer.CorrectSortingOrder = result;
                        forcer.ForceCorrectOrder();
                    }
                }
                
                // 通过组件控制器设置
                AddSortingOrderControllerTo(mr, instanceID);
                
                // 最终再次直接设置
                if (mr != null)
                {
                    mr.sortingOrder = result;
                    
                    // 检查当前值是否仍然错误
                    if (mr.sortingOrder != result)
                    {
                      //  Debug.LogError($"[严重错误] 层级值无法修正! 物体={gameObject.name}[{instanceID}], " +
                            //          $"坐标=({originalX:F2}, {originalY:F2}), 错误层级={mr.sortingOrder}, 应为={result}");
                        
                        // 延迟一帧再强制设置
                        StartCoroutine(DelayedForceSorting(mr, result));
                    }
                }
            }
            catch (System.Exception ex)
            {
             //   Debug.LogError($"SetSpineSortingOrderBySelfPosition异常: {ex.Message} | {ex.StackTrace}");
            }
        }
        
        // 延迟一帧强制设置排序层级
        private IEnumerator DelayedForceSorting(MeshRenderer renderer, int value)
        {
            yield return null; // 等待一帧
            if (renderer != null)
            {
                renderer.sortingOrder = value;
              //  Debug.Log($"[延迟修正] 物体={renderer.gameObject.name}, 设置层级={value}, 实际层级={renderer.sortingOrder}");
            }
        }

        // 启动全局渲染层级锁定系统
        private void StartGlobalSortingOrderLockSystem()
        {
            if (_sortingOrderLockEnabled) return;
            
            _sortingOrderLockEnabled = true;
           // Debug.Log("初始化全局渲染层级强制系统");
            
            // 安装渲染前事件处理器
            Camera.onPreRender += OnPreRenderForceSortingOrder;
            
            // 启动强制协程
            StartCoroutine(RenderForcingCoroutine());
        }
        
        // 将渲染器注册到强制执行系统
        public void RegisterRendererForForcing(MeshRenderer renderer, int instanceID, MonoBehaviour owner)
        {
            if (renderer == null) return;
            
            // 检查是否已经注册
            if (_renderForcers.TryGetValue(instanceID, out RenderForcer forcer))
            {
                // 已存在，更新引用
                forcer.Renderer = renderer;
                forcer.Owner = owner;
                forcer.LastUpdateTime = Time.time;
                return;
            }
            
            // 创建新的强制执行器
            RenderForcer newForcer = new RenderForcer
            {
                Renderer = renderer,
                InstanceID = instanceID,
                Owner = owner,
                LastUpdateTime = Time.time
            };
            
            // 如果缓存中有正确值，设置到强制执行器
            if (_correctSortingOrderCache.TryGetValue(instanceID, out int correctValue))
            {
                newForcer.CorrectSortingOrder = correctValue;
            }
            
            // 应用正确的层级值
            renderer.sortingOrder = newForcer.CorrectSortingOrder;
            
            // 添加到强制执行器字典
            _renderForcers[instanceID] = newForcer;
            
          //  Debug.Log($"已注册渲染器: {owner?.name}, ID={instanceID}, 初始层级={newForcer.CorrectSortingOrder}");
        }
        
        // 在每个渲染帧之前强制应用正确的层级值
        private static void OnPreRenderForceSortingOrder(Camera cam)
        {
            // 忽略场景视图相机等
            if (!cam.CompareTag("MainCamera")) return;
            
            // 防止递归调用
            if (_interceptingSort) return;
            
            _interceptingSort = true;
            
            try
            {
                // Unity排序层级范围常量
                const int MIN_SORTING_VALUE = -32768;
                const int MAX_SORTING_VALUE = 32767;
                
                // 强制应用所有正确的层级值
                foreach (var forcer in _renderForcers.Values)
                {
                    if (forcer.Renderer != null && forcer.Owner != null)
                    {
                        // 1. 重新计算以获取最新的正确层级值
                        if (forcer.Owner is ThingBehaviour thingBehaviour)
                        {
                            thingBehaviour.SetSpineSortingOrderBySelfPosition();
                            
                            // 从缓存中获取最新的正确值
                            int instanceID = forcer.InstanceID;
                            if (_correctSortingOrderCache.TryGetValue(instanceID, out int latestCorrectValue))
                            {
                                // 确保值在有效范围内
                                if (latestCorrectValue < MIN_SORTING_VALUE)
                                    latestCorrectValue = MIN_SORTING_VALUE;
                                if (latestCorrectValue > MAX_SORTING_VALUE)
                                    latestCorrectValue = MAX_SORTING_VALUE;
                                
                                // 更新强制执行器中的正确值
                                forcer.CorrectSortingOrder = latestCorrectValue;
                            }
                        }
                        
                        // 2. 检查当前值是否正确
                        if (forcer.Renderer.sortingOrder != forcer.CorrectSortingOrder)
                        {
                            // 记录错误并强制修正
                         //   Debug.LogWarning($"[渲染前修正] 物体={forcer.Owner.name}, ID={forcer.InstanceID}, " +
                            //               $"错误层级={forcer.Renderer.sortingOrder}, 修正为={forcer.CorrectSortingOrder}");
                            
                            // 3. 强制应用正确值
                            forcer.Renderer.sortingOrder = forcer.CorrectSortingOrder;
                            forcer.ForceCorrectOrder();
                            
                            // 4. 通过SortingOrderController组件再次应用
                            SortingOrderController controller = forcer.Renderer.GetComponent<SortingOrderController>();
                            if (controller != null)
                            {
                                controller.CorrectSortingOrder = forcer.CorrectSortingOrder;
                                controller.ApplyCorrectSortingOrder();
                            }
                        }
                    }
                }
                
                // 检查当前帧中的所有SkeletonAnimation组件
                SkeletonAnimation[] allVisibleSpines = Object.FindObjectsOfType<SkeletonAnimation>();
                foreach (SkeletonAnimation spine in allVisibleSpines)
                {
                    if (spine != null)
                    {
                        MeshRenderer mr = spine.GetComponent<MeshRenderer>();
                        if (mr != null && (mr.sortingOrder >= 0 || mr.sortingOrder == 0))
                        {
                            // 安全计算一个合理的层级值
                            float yPos = spine.transform.position.y;
                            float xPos = spine.transform.position.x;
                            
                            // 使用新的计算方式
                            float scaleFactor = 100.0f;
                            int yPart = -(int)(yPos * scaleFactor);
                            
                            // 确保Y部分不会导致溢出
                            if (yPart < MIN_SORTING_VALUE / 2)
                                yPart = MIN_SORTING_VALUE / 2;
                            if (yPart > MAX_SORTING_VALUE / 2)
                                yPart = MAX_SORTING_VALUE / 2;
                            
                            int xPart = (int)(xPos * 10) % 10;
                            int safeValue = yPart + xPart - 100;
                            
                            // 确保结果在合法范围内并且为负数
                            if (safeValue >= 0)
                                safeValue = -1000;
                            
                            // 最终安全检查
                            if (safeValue < MIN_SORTING_VALUE)
                                safeValue = MIN_SORTING_VALUE;
                            if (safeValue > MAX_SORTING_VALUE)
                                safeValue = MAX_SORTING_VALUE;
                            
                          //  Debug.LogError($"[渲染前扫描] 发现错误层级! 物体={spine.gameObject.name}, " +
                                //         $"错误层级={mr.sortingOrder}, 应用安全值={safeValue}");
                            
                            // 应用安全值
                            mr.sortingOrder = safeValue;
                            
                            // 将该对象标记为需要彻底修复
                            ThingBehaviour owner = spine.GetComponentInParent<ThingBehaviour>();
                            if (owner != null)
                            {
                                owner.SetSpineSortingOrderBySelfPosition();
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
               // Debug.LogError($"渲染前强制应用层级值时出错: {ex.Message}");
            }
            finally
            {
                _interceptingSort = false;
            }
        }
        
        // 持续强制协程 - 在多个时间点强制应用正确的层级值
        private System.Collections.IEnumerator RenderForcingCoroutine()
        {
            // 等待初始化完成
            yield return new WaitForSeconds(0.5f);
            
        //    Debug.Log("层级强制系统已启动");
            
            // 查找并注册所有现有的SkeletonAnimation组件
            SkeletonAnimation[] allSpines = FindObjectsOfType<SkeletonAnimation>();
            int forceCount = 0;
            
            foreach (SkeletonAnimation spine in allSpines)
            {
                if (spine == null) continue;
                
                MeshRenderer mr = spine.GetComponent<MeshRenderer>();
                if (mr == null) continue;
                
                // 获取物体ID
                int instanceID = spine.gameObject.GetInstanceID();
                
                // 找到所属的ThingBehaviour
                ThingBehaviour thing = spine.GetComponentInParent<ThingBehaviour>();
                if (thing != null)
                {
                    // 注册到强制系统
                    RegisterRendererForForcing(mr, instanceID, thing);
                    forceCount++;
                    
                    // 确保有正确的层级值
                    thing.SetSpineSortingOrderBySelfPosition();
                }
            }
            
            Debug.Log($"强制系统初始化完成 - 已添加{forceCount}个强制执行器");
            
            // 特殊的等待指令，确保在渲染之前执行
            WaitForEndOfFrame endOfFrameWait = new WaitForEndOfFrame();
            
            // 帧计数
            int frameCounter = 0;
            
            while (true)
            {
                // 每帧开始时扫描
                yield return new WaitForFixedUpdate();
                
                // 在固定帧率时强制应用所有层级值
                foreach (var forcer in _renderForcers.Values)
                {
                    forcer.ForceCorrectOrder();
                }
                
                // 等待帧结束
                yield return endOfFrameWait;
                
                // 增加帧计数
                frameCounter++;
                
                // 每10帧扫描新对象
                if (frameCounter % 10 == 0)
                {
                    // 防止递归
                    if (_interceptingSort) continue;
                    
                    _interceptingSort = true;
                    
                    try
                    {
                        // 查找新的SkeletonAnimation组件
                        SkeletonAnimation[] newSpines = FindObjectsOfType<SkeletonAnimation>();
                        
                        foreach (SkeletonAnimation spine in newSpines)
                        {
                            if (spine == null) continue;
                            
                            int instanceID = spine.gameObject.GetInstanceID();
                            
                            // 检查是否已注册
                            if (_renderForcers.ContainsKey(instanceID)) continue;
                            
                            MeshRenderer mr = spine.GetComponent<MeshRenderer>();
                            if (mr == null) continue;
                            
                            // 找到所属的ThingBehaviour
                            ThingBehaviour thing = spine.GetComponentInParent<ThingBehaviour>();
                            if (thing != null)
                            {
                                // 注册到强制系统
                                RegisterRendererForForcing(mr, instanceID, thing);
                                
                                // 确保有正确的层级值
                                thing.SetSpineSortingOrderBySelfPosition();
                            }
                        }
                        
                        // 清理无效的强制执行器
                        List<int> toRemove = new List<int>();
                        foreach (var kvp in _renderForcers)
                        {
                            if (kvp.Value.Renderer == null || kvp.Value.Owner == null)
                            {
                                toRemove.Add(kvp.Key);
                            }
                        }
                        
                        foreach (int key in toRemove)
                        {
                            _renderForcers.Remove(key);
                        }
                    }
                    catch (System.Exception ex)
                    {
                      //  Debug.LogError($"扫描新对象时出错: {ex.Message}");
                    }
                    finally
                    {
                        _interceptingSort = false;
                    }
                }
                
                // 等待一帧
                yield return null;
            }
        }
        
        // 取消注册时的清理 
        private void CleanupRegistrations() // 已废弃，保留方法签名避免编译错误
        {
            // 所有功能已移至OnDestroy方法
            Debug.Log("清理代码已移至OnDestroy方法，此方法已废弃");
            // 不执行任何实际操作，避免重复清理
        }

        /// <summary>
        /// 添加排序层级控制器到指定渲染器
        /// </summary>
        public void AddSortingOrderControllerTo(MeshRenderer renderer, int instanceID)
        {
            if (renderer == null) return;
            
            // Unity排序层级范围常量
            const int MIN_SORTING_VALUE = -32768;
            const int MAX_SORTING_VALUE = 32767;
            
            // 检查缓存是否已经有此渲染器的控制器
            if (_controllerDict.TryGetValue(renderer, out SortingOrderController existingController))
            {
                // 更新现有控制器
                if (_correctSortingOrderCache.TryGetValue(instanceID, out int correctValue))
                {
                    // 确保值在有效范围内
                    if (correctValue < MIN_SORTING_VALUE)
                        correctValue = MIN_SORTING_VALUE;
                    if (correctValue > MAX_SORTING_VALUE)
                        correctValue = MAX_SORTING_VALUE;
                    
                    existingController.CorrectSortingOrder = correctValue;
                    existingController.ApplyCorrectSortingOrder();
                }
                return;
            }
            
            // 尝试获取现有组件
            SortingOrderController controller = renderer.GetComponent<SortingOrderController>();
            
            // 如果没有，添加新组件
            if (controller == null)
            {
                controller = renderer.gameObject.AddComponent<SortingOrderController>();
            }
            
            // 初始化控制器
            int correctOrder = -1000; // 默认安全负值
            if (_correctSortingOrderCache.TryGetValue(instanceID, out int cachedOrder))
            {
                // 确保值在有效范围内
                if (cachedOrder < MIN_SORTING_VALUE)
                    cachedOrder = MIN_SORTING_VALUE;
                if (cachedOrder > MAX_SORTING_VALUE)
                    cachedOrder = MAX_SORTING_VALUE;
                
                correctOrder = cachedOrder;
            }
            
            controller.Initialize(renderer, correctOrder);
            
            // 添加到字典中
            _controllerDict[renderer] = controller;
            
            // 确保值立即生效
            renderer.sortingOrder = correctOrder;
        }
    }
}