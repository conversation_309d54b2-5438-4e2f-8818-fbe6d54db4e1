﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     连射子弹(怪物的枪)
    /// </summary>
    public class ThingCdExecutor_2001 : MonsterGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            //Debug.Log($"怪物连射子弹DoShoot开始, Thing={Thing?.GetType().Name}, MonsterThing={(MonsterThing != null ? MonsterThing.Guid : "null")}");
            
            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                Debug.LogWarning($"射程为0，无法发射子弹");
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                Debug.LogWarning($"没有找到攻击目标，无法发射子弹");
                return;
            }

            // 攻击距离还够不到目标，就不发子弹了。
            if (distanceEnemy.Distance > gunRange)
            {
                Debug.LogWarning($"攻击距离不够，无法发射子弹: 距离={distanceEnemy.Distance}, 射程={gunRange}");
                return;
            }

            // 调用基类的DoShoot方法(MonsterGunCdExecutor)播放攻击动画
            Debug.Log($"准备调用基类MonsterGunCdExecutor.DoShoot方法以播放攻击动画");
            base.DoShoot(token).Forget();
            Debug.Log($"已调用基类的DoShoot方法");

            await UniTask.SwitchToMainThread(token);
            Debug.Log($"已切换到主线程");

            // 怪物已销毁，不发子弹
            if (MonsterThing == null)
            {
                Debug.LogWarning($"怪物已销毁，无法发射子弹");
                return;
            }

            //Debug.Log($"准备发射子弹");
            
            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , MonsterThing.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向
                // Vector3 shootDir = (distanceEnemy.Thing2.Position - MonsterThing.Position).normalized;
                Vector3 shootDir = Vector3.down;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstburstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    BurstOne(cts_Skill.Token, (float)x, distanceEnemy.Thing2, shootDir, bulletQty,
                            burstburstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();

                OnAfterShoot();
            }
            catch
            {
                // ignore
            }
            
            //Debug.Log($"怪物连射子弹DoShoot结束");
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });
                //Debug.Log($"一轮发射子弹数:{bulletQty}");

                // 追踪位置设为敌人当前位置
                // Vector3? trackPos = attackBaseDirFollowThing?.Position;
                Vector3? trackPos = null;

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, null, trackPos, angle,
                        maxPenetrateTimes,
                        maxBounceTimes, maxSeparateTimes);
                    if (bullet == null)
                    {
                        return;
                    }

                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    bullet.Position = MonsterThing.Position; // + 0.1f * dir_1;
                    bullet.MoveDirection_Straight.Value = dir_1;

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }
    }
}