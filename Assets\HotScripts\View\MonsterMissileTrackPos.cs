// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using HotScripts;

using ThingCdExecutors;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     导弹(定点打击)
    /// </summary>
    public class MonsterMissileTrackPos : MonsterBulletBase
    {
        /// <summary>
        ///     轨迹生成器(包含发射器)
        /// </summary>
        public MissileLocusGenerator MissileLocusGenerator { get; set; }

        /// <summary>
        ///     导弹轨迹(关键点)
        /// </summary>
        public List<Vector3> Locus { get; set; }

        /// <summary>
        ///     是用上面还是下面的轨迹
        /// </summary>
        public bool LocusDown { get; set; }

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            await base.OnAfterInitView();

            // 添加空检查，避免空引用异常
            if (MissileLocusGenerator == null || !MissileLocusGenerator.TargetPosition.HasValue)
            {
                Debug.LogWarning("导弹轨迹生成器或目标位置为空，无法完成导弹初始化");
                return;
            }

            // 发射时不让怪物移动
            MonsterThing.Monster.MonsterMoveAI.StopMoveAI();

            float bulletLocusDuration = (float)GunThing.GetTotalDouble(PropType.BulletLocusDuration).FirstOrDefault();
            if (bulletLocusDuration > 0)
            {
                // 落点预示
                EffectMgr.Instance.ShowEffect(EffectPath.BornCircle,
                    MissileLocusGenerator.TargetPosition.Value, 2, null, bulletLocusDuration).Forget();

                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration));
            }
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);
                
                gameObject.SetActive(false);

                // base.TurnToPool().Forget();
                // 隐藏导弹发射器(坐标系对象)
                MissileLocusGenerator.MissileEjector.gameObject.SetActive(false);

                // 延时1分钟后置为null
                await UniTask.Delay(TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        #region 爆炸

        /// <summary>
        ///     在目标位置爆炸后归还到子弹池
        /// </summary>
        /// <param name="token"></param>
        public void DoExplose(CancellationToken token)
        {
            Vector3 explosePos;
            
            // 根据是否为垂直轰炸模式确定爆炸位置
            if (BulletThing.CdExecutor is ThingCdExecutor_13)
            {
                // 垂直轰炸模式：使用当前位置作为爆炸位置
                explosePos = transform.position;
            }
            else
            {
                // 原有抛物线模式：使用目标位置作为爆炸位置
                if (MissileLocusGenerator == null || !MissileLocusGenerator.TargetPosition.HasValue)
                {
                    Debug.LogWarning("导弹轨迹生成器或目标位置为空，无法执行爆炸效果");
                    TurnToPool().Forget();
                    return;
                }
                explosePos = MissileLocusGenerator.TargetPosition.Value;
            }
            
            // 根据CommonProp表字段PropType=ExplosePriority触发爆炸逻辑
            // 无论是否碰到角色，都会触发爆炸
            double exploseRate = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();
            
            // 如果配置了爆炸概率，按概率触发爆炸；如果没有配置或为0，则必定爆炸
            bool shouldExplode = exploseRate <= 0 || RandomNum.RandomDouble(0, 1) <= exploseRate;
            
            if (shouldExplode)
            {
                // 爆炸声音
                string exploseSound = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(exploseSound))
                {
                    AudioPlayer.Instance.PlaySound(exploseSound).Forget();
                }
                
                // 爆炸特效
                string exploseEffect = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
                // 爆炸半径
                float exploseRadius = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(exploseEffect))
                {
                    EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect),
                        explosePos, exploseRadius).Forget();
                }

                // 检查爆炸半径内的角色
                if (exploseRadius > 0)
                {
                    var actor = SingletonMgr.Instance.BattleMgr.Actor;
                    if (actor?.CircularArea2D != null &&
                        actor.CircularArea2D.IsIntersect(new CircularArea2D
                        {
                            Center = explosePos, Radius = exploseRadius
                        }))
                    {
                        (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, 1, actor);

                        if (damage > 0)
                        {
                            // 受击方先接受枪携带的Buff
                            actor.ReceiveBuffByBulletHit(BulletThing.CdExecutor.Thing, BuffRecvType.Explose);

                            // 角色接受伤害
                            actor.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
                        }
                    }
                }

                // 爆炸后出生新怪（仅在原有模式下）
                if (!(BulletThing.CdExecutor is ThingCdExecutor_13))
                {
                    SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.BornMonster(
                        explosePos, 3,
                        BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterList).ConvertAll(x => (int)x),
                        BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterCountList).ConvertAll(x => (int)x)
                    );
                }
            }

            // 界面还给子弹池
            TurnToPool().Forget();
        }

        #endregion

        #region 移动

        public override void StartMove()
        {
            base.StartMove();

            // 允许怪物移动
            MonsterThing.Monster.MonsterMoveAI.StartMove();
        }

        /// <summary>
        ///     任务实现:按轨迹移动（垂直轰炸模式）
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                // 检查是否为垂直轰炸模式（ShootMethod=13）
                if (BulletThing.CdExecutor is ThingCdExecutor_13)
                {
                    await DoVerticalBombardmentMove(token);
                }
                else
                {
                    // 原有的抛物线移动逻辑
                    await DoParabolicMove(token);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <summary>
        /// 垂直轰炸移动逻辑
        /// </summary>
        private async UniTask DoVerticalBombardmentMove(CancellationToken token)
        {
            // 获取停留时间参数
            float stayTime = GetStayTimeFromRemark();
            
            // 显示子弹图片
            ImgElement.SetActive(true);
            
            // 第一阶段：停留
            if (stayTime > 0)
            {
                await UniTask.Delay(TimeSpan.FromSeconds(stayTime), cancellationToken: token);
            }
            
            // 第二阶段：垂直下落
            float moveInterval = 0.03f;
            Vector3 targetPos = MissileLocusGenerator.TargetPosition.Value;
            
            for (;; await UniTask.Delay(TimeSpan.FromSeconds(moveInterval), cancellationToken: token))
            {
                if (token.IsCancellationRequested)
                {
                    return;
                }
                
                if (OnBeforeMoveOne())
                {
                    return;
                }
                
                try
                {
                    if (MoveOne_VerticalFall(moveInterval, targetPos))
                    {
                        DoExplose(token);
                        return;
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
            }
        }

        /// <summary>
        /// 原有的抛物线移动逻辑
        /// </summary>
        private async UniTask DoParabolicMove(CancellationToken token)
        {
            float duration = 0.03f;
            // 三次贝塞尔曲线(自动生成控制点)
            CubicBezierPath cubicBezier = new(Locus.ToArray());
            // 曲线距离(近似值)
            float distance = cubicBezier.ComputeApproxLength();
            // 速度
            float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            // 飞行全线所需时长
            float totalDuration = distance / speed;
            // 随机选择一边(上或下)
            LocusDown = RandomNum.RandomInt(0, 10000) < 5000;

            // 显示子弹图片
            ImgElement.SetActive(true);

            for (;; await UniTask.Delay(TimeSpan.FromSeconds(duration), cancellationToken: token))
            {
                if (token.IsCancellationRequested)
                {
                    return;
                }

                if (Time.deltaTime <= 0)
                {
                    continue;
                }

                if (OnBeforeMoveOne())
                {
                    return;
                }

                try
                {
                    // var line = MoveOne_PositionMove(Time.deltaTime);
                    if (MoveOne_PositionMove(duration, totalDuration, cubicBezier))
                    {
                        DoExplose(token);
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
            }
        }

        /// <summary>
        /// 从子弹的Remark属性中获取停留时间
        /// </summary>
        private float GetStayTimeFromRemark()
        {
            try
            {
                // 从子弹属性中获取Remark
                string remark = BulletThing.GetTotalString(PropType.Remark).FirstOrDefault();
                if (string.IsNullOrEmpty(remark))
                {
                    // 尝试从配置中获取
                int bulletId = (int)BulletThing.CdExecutor.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault();
                var bulletCfg = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CsvTables.BulletCsv>().Pb.CSVTable
                    .FirstOrDefault(x => x.BulletId == bulletId);
                remark = bulletCfg?.Remark;
                }
                
                if (string.IsNullOrEmpty(remark))
                    return 0f;
                
                string[] parts = remark.Split(';');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float stayTime))
                {
                    return stayTime;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"解析子弹Remark参数失败: {ex.Message}");
            }
            
            return 0f; // 默认停留时间
        }

        /// <summary>
        /// 垂直下落移动逻辑
        /// </summary>
        private bool MoveOne_VerticalFall(float deltaTime, Vector3 targetPos)
        {
            // 检查是否到达目标Y坐标
            if (transform.position.y <= targetPos.y)
            {
                return true; // 到达目标，触发爆炸
            }
            
            // 获取下落速度
            float fallSpeed = GetFallSpeed();
            
            // 计算新位置（垂直向下）
            Vector3 newPosition = transform.position;
            newPosition.y -= fallSpeed * deltaTime;
            
            // 确保不会超过目标Y坐标
            if (newPosition.y < targetPos.y)
            {
                newPosition.y = targetPos.y;
            }
            
            transform.position = newPosition;
            
            // 设置朝向（向下）
            transform.up = Vector3.down;
            
            // 检查碰撞
            CheckCollisionWithActor();
            
            return newPosition.y <= targetPos.y;
        }

        /// <summary>
        /// 获取子弹下落速度
        /// </summary>
        private float GetFallSpeed()
        {
            // 优先从枪配置中获取速度
            if (GunThing != null)
            {
                float gunSpeed = (float)GunThing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                if (gunSpeed > 0) return gunSpeed;
            }
            
            // 其次从子弹配置中获取速度
            if (BulletThing?.CdExecutor?.Thing != null)
            {
                float bulletSpeed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                if (bulletSpeed > 0) return bulletSpeed;
            }
            
            return 10f; // 默认下落速度
        }

        /// <summary>
        /// 检查与角色的碰撞
        /// </summary>
        private void CheckCollisionWithActor()
        {
            try
            {
                var actor = SingletonMgr.Instance.BattleMgr.Actor;
                if (actor?.CircularArea2D != null)
                {
                    // 检查是否与角色碰撞
                    if (actor.CircularArea2D.IsIntersect(new CircularArea2D
                    {
                        Center = transform.position,
                        Radius = 0.5f // 子弹碰撞半径
                    }))
                    {
                        // 计算伤害
                        (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, 1, actor);
                        
                        if (damage > 0)
                        {
                            // 造成伤害
                            actor.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"检查碰撞时发生错误: {ex.Message}");
            }
        }

        /// <inheritdoc />
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            // // 达到子弹最大存活时长,结束
            // if (BulletThing.LifeBeginTime.Value +
            //     BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault() <= Time.time)
            // {
            //     return true;
            // }

            return false;
        }

        /// <inheritdoc />
        protected override void OnMoveEnd()
        {
            try
            {
                // 还给子弹池
                TurnToPool().Forget();
            }
            catch
            {
                // ignored
            }
        }

        /// <summary>
        ///     移动一次。传送至贝塞尔曲线中按时间进度对应的点
        /// </summary>
        /// <returns>是否已走完整个曲线</returns>
        public virtual bool MoveOne_PositionMove(float duration, float totalDuration, CubicBezierPath cubicBezier)
        {
            if (totalDuration <= 0)
            {
                return true;
            }

            // 移动计时
            MoveDuration.Value += duration;

            // 时间进度
            float progress = Mathf.Clamp01(MoveDuration.Value / totalDuration);

            #region 导弹前进一次(直接修改位置)

            // 按上边轨迹计算坐标(发射器坐标系)
            Vector3 p1 = cubicBezier.GetPointNorm(progress);
            Vector3 tan1 = cubicBezier.GetTangentNorm(progress);

            // 如果走的是下边轨迹
            if (LocusDown)
            {
                p1 = new Vector3(p1.x, -p1.y);
                tan1 = new Vector3(tan1.x, -tan1.y);
            }

            // 转换为世界坐标
            Vector3 p = MissileLocusGenerator.MissileEjector.transform.TransformPoint(p1);
            Vector3 dir = MissileLocusGenerator.MissileEjector.transform.TransformPoint(tan1);

            transform.position = p;
            transform.right = dir - MissileLocusGenerator.MissileEjector.transform.position;

            #endregion

            return progress >= 1;
        }

        #endregion
    }
}