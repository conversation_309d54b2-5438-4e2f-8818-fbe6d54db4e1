﻿using System;
using System.Linq;

using Apq.ChangeBubbling;

using Thing;

using UnityEngine;

namespace View
{
	public class PlayerActor : CreatureBase
	{
		/// <summary>
		/// 玩家角色物件(数据)
		/// </summary>
		public ActorThing ActorThing => Thing as ActorThing;
        /// <summary>
        /// 角色移动组件
        /// </summary>
        public PlayerMove PlayerMove { get; set; }
        
        /// <summary>
        /// 角色的骨骼动画组件
        /// </summary>
        private Spine.Unity.SkeletonAnimation SkeAni { get; set; }
        
        // 朝向翻转时间间隔（秒）
        private const float DIRECTION_FLIP_INTERVAL = 1.0f;
        // 上次朝向翻转的时间
        private float lastDirectionFlipTime = -DIRECTION_FLIP_INTERVAL; // 初始值设为负值，确保第一次可以立即翻转
        // 当前朝向
        private int currentDirection = 0;
        // 朝向稳定延迟时间
        private const float DIRECTION_STABILIZE_DELAY = 0.2f;
        // 上一次请求改变方向的时间
        private float lastDirectionRequestTime = 0f;
        // 请求的方向
        private int requestedDirection = 0;
        // 是否有待处理的方向请求
        private bool hasPendingDirectionRequest = false;

		public override void Start()
		{
            HpBar.bar.gameObject.SetActive(true);
            ActorThing.Hp.Changed += Hp_Changed;
            ActorThing.Armor.Changed += Armor_Changed;
        }

        public override void DoSyncToThing()
        {
            base.DoSyncToThing();
            SetSpineSortingOrderBySelfPosition();
            HpBar.SetDisplayHealth((float)(ActorThing.Hp.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            HpBar.SetDisplayArmorBar((float)(ActorThing.Armor.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
        }

        private void OnDisable()
        {
            ActorThing.Hp.Changed -= Hp_Changed;
            ActorThing.Armor.Changed -= Armor_Changed;
        }

        private void Armor_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue)
            {
                HpBar.SetDisplayArmorBar((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            }
        }

        private void Hp_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue && e.OriginalValue is double oldValue)
            {
                HpBar.SetDisplayHealth((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
                if (newValue > oldValue)
                {
                    //回飘字
                    HudMgr.Instance.SpwanDamageHud(hudComp =>
                    {
                        // 使用角色原始位置
                        hudComp.Init(transform.position);
                        hudComp.SetHpRecoverNumber(Mathf.CeilToInt((float)newValue - (float)oldValue));
                    }).Forget();
                }
            }
        }

        /// <summary>
        /// 设置角色朝向，添加时间限制，防止快速翻转
        /// </summary>
        /// <param name="value">朝向值，1为左，-1为右</param>
        public new void SetSpineModelDirection(int value)
        {
            // 确保值有效
            if (value != 1 && value != -1) return;
            
            // 记录最近一次请求的方向和时间
            requestedDirection = value;
            lastDirectionRequestTime = Time.time;
            hasPendingDirectionRequest = true;
        }
        
        private void Update()
        {
            // 必须调用基类的Update方法，这样ThingBehaviour中的位置同步逻辑才会执行
            base.Update();
            
            // 处理延迟的方向请求
            if (hasPendingDirectionRequest)
            {
                // 检查是否已经稳定一定时间没有新的方向请求
                if (Time.time - lastDirectionRequestTime >= DIRECTION_STABILIZE_DELAY)
                {
                    // 方向已稳定，可以考虑执行翻转
                    hasPendingDirectionRequest = false;
                    
                    // 检查是否需要翻转以及是否符合翻转时间限制
                    if (requestedDirection != currentDirection && (Time.time - lastDirectionFlipTime) >= DIRECTION_FLIP_INTERVAL)
                    {
                        // 更新当前朝向和翻转时间
                        currentDirection = requestedDirection;
                        lastDirectionFlipTime = Time.time;
                        
                        // 调用基类方法实际执行翻转
                        base.SetSpineModelDirection(requestedDirection);
                        
                        // 添加日志便于调试
                         ///Debug.Log($"角色翻转: 方向={requestedDirection}, 时间={Time.time:F2}");
                    }
                }
            }
            
            // 检测角色移动并播放相应动画
            if (PlayerMove != null && PlayerMove.IsMoving)
            {
                // 获取当前动画名称
                string currentAnimName = null;
                if (SkeAni != null && SkeAni.state.GetCurrent(0) != null)
                {
                    currentAnimName = SkeAni.state.GetCurrent(0).Animation.Name;
                }
                
                // 如果当前不是在播放攻击动画，则播放移动动画
                if (currentAnimName != "attack01")
                {
                    PlayAnimation("move01", true, false);
                }
            }
            
            // 像怪物一样在每帧更新渲染层级
            SetSpineSortingOrderBySelfPosition();
        }

        /// <summary>
        /// 获取或设置是否拒绝移动
        /// </summary>
        public bool DenyMove
        {
            get => PlayerMove.DenyMove;
            set => PlayerMove.DenyMove = value;
        }

        //public void PerformDash()
        //{
        //	if (PlayerPrefs.GetInt("DashTutorial2") == 1)
        //	{
        //		SingletonMgr.Instance.BattleMgr.TimeManager.SetTimescale(1.0f);
        //		//TODO Mobile and Console Controls
        //		//InputController.instance.dashButtonGlow.SetActive(false);
        //		//InputController.instance.gameObject.SetActive(false);
        //		PlayerPrefs.SetInt("DashTutorial2", 0);
        //	}
        //}

        /// <summary>
        /// 设置角色模型渲染顺序，与怪物保持一致
        /// </summary>
        public new void SetSpineSortingOrderBySelfPosition()
        {
            try
            {
                // 打印当前对象路径用于调试
                string fullPath = GetFullObjectPath(transform);
                 ///Debug.Log($"角色渲染层级: 当前对象路径 = {fullPath}");
                
                // 记录原始Y坐标值便于调试
                float originalY = transform.position.y;
                 ///Debug.Log($"角色渲染层级: 原始Y坐标 = {originalY}");

                // 首先按照ThingBehaviour中相同的方式获取SkeletonAnimation
                Spine.Unity.SkeletonAnimation skeAni = GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
                
                // 如果基本方法找不到，再尝试针对角色预制体的特殊路径
                if (skeAni == null)
                {
                    Transform spineTransform = transform.Find("Spine_WXR003(Clone)/Spine");
                    if (spineTransform == null)
                    {
                        spineTransform = transform.Find("Spine_WXR003/Spine");
                    }
                    
                    if (spineTransform != null)
                    {
                        skeAni = spineTransform.GetComponent<Spine.Unity.SkeletonAnimation>();
                    }
                }

                // 输出调试信息
                if (skeAni != null)
                {
                     ///Debug.Log($"角色渲染层级: 找到SkeletonAnimation = {GetFullObjectPath(skeAni.transform)}");
                }
                else
                {
                     ///Debug.Log("角色渲染层级: 未找到任何SkeletonAnimation组件");
                    return;
                }
                
                MeshRenderer mr = skeAni.GetComponent<MeshRenderer>();
                if (mr != null)
                {
                    // 获取物体的唯一标识
                    int instanceID = gameObject.GetInstanceID();
                    
                    int oldSortingOrder = mr.sortingOrder;
                    
                    // 使用安全的计算方法计算Y部分
                    // 先检查Y值是否在安全范围内，防止溢出
                    double yValue = transform.position.y * 10000;
                    int yPart;
                    
                    // 检查是否会溢出int范围
                    if (yValue > int.MaxValue || yValue < int.MinValue)
                    {
                        // 如果会溢出，使用一个安全的方法计算
                        yPart = (transform.position.y >= 0) ? 
                               -int.MaxValue : // Y为正，结果应该是负的最大值
                               int.MinValue;   // Y为负，结果应该是最小值（负）
                    }
                    else
                    {
                        // 正常计算并取负值
                        yPart = -(int)(transform.position.y * 10000);
                    }
                    
                    // X部分计算
                    int xPart = (int)(transform.position.x * 10) % 10;
                    
                    // 加上基础偏移
                    int newSortingOrder = yPart + xPart + 100;
                    
                    // 强制确保结果为负数
                    if (newSortingOrder >= 0)
                    {
                        newSortingOrder = -System.Math.Abs(newSortingOrder) - 100; // 额外 -100 确保结果一定是负数
                    }
                    
                    // 安全检查：确保sortingOrder在合理范围内
                    if (newSortingOrder > 0 || newSortingOrder < -10000000)
                    {
                        // 如果计算结果异常，使用安全值
                        newSortingOrder = -1000000;
                       // Debug.LogWarning($"角色检测到异常计算结果: 坐标({transform.position.x:F2}, {transform.position.y:F2}), 使用安全值={newSortingOrder}");
                    }
                    
                    // 检查是否需要添加控制器
                    if (mr != null)
                    {
                        // 获取或创建ThingBehaviour组件（PlayerActor本身就是ThingBehaviour的子类）
                        ThingBehaviour thing = this;
                        
                        // 添加渲染层级控制器
                        thing.AddSortingOrderControllerTo(mr, instanceID);
                    }
                    
                    // 更新缓存中的正确值 - 全局系统会使用这个值进行强制修正
                    ThingBehaviour._correctSortingOrderCache[instanceID] = newSortingOrder;
                    
                    // 立即应用计算结果
                    mr.sortingOrder = newSortingOrder;
                    
                    // 检查当前值是否明显错误
                    bool isCurrentValueWrong = oldSortingOrder >= 0;
                    
                    // 检查缓存中是否有此物体的记录
                    if (ThingBehaviour._correctSortingOrderCache.TryGetValue(instanceID, out int cachedOrder))
                    {
                        // 如果当前值与缓存值相差太大，也认为是错的
                        if (System.Math.Abs(oldSortingOrder - cachedOrder) > 10000)
                        {
                            isCurrentValueWrong = true;
                        }
                    }
                    
                    // 记录修复情况
                    if (isCurrentValueWrong)
                    {
                       // Debug.LogWarning($"角色层级预防系统已接管: 坐标=({transform.position.x:F2}, {transform.position.y:F2}), 原始层级={oldSortingOrder}, 修正层级={newSortingOrder}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                // 捕获并记录任何异常，确保不会导致游戏崩溃
              //  Debug.LogError($"角色SetSpineSortingOrderBySelfPosition异常: {ex.Message} | {ex.StackTrace}");
            }
        }
        
        // 获取对象的完整路径，用于调试
        private string GetFullObjectPath(Transform transform)
        {
            string path = transform.name;
            Transform parent = transform.parent;
            
            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }
            
            return path;
        }
    }

}
