﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using CsvTables;

using DataStructure;

using Props;

using ThingCdExecutors;

using UnityEngine;

using View;

using ViewModel;

using X.PB;

using Cysharp.Threading.Tasks;

namespace Thing
{
    /// <summary>
    /// 怪物死亡原因枚举
    /// </summary>
    public enum MonsterDeathCause
    {
        /// <summary>
        /// 被玩家/子弹打死
        /// </summary>
        KilledByPlayer = 0,
        
        /// <summary>
        /// 边界自杀
        /// </summary>
        BoundarySuicide = 1
    }

    /// <summary>
    ///     怪物物件(数据)
    /// </summary>
    /// <remarks>
    ///     加载属性的顺序：
    ///     CsvRow_BattleBrushEnemy
    ///     怪物的固有属性
    ///     创建枪及其固有属性
    ///     怪物和枪的附着属性分发到目标上
    ///     怪物提升、枪提升
    /// </remarks>
    public class MonsterThing : CreatureThing
    {
        private BattleBrushEnemy.Item CsvRow_BattleBrushEnemy_m;
        private List<GunThing> bossCGGuns = new List<GunThing>();
        private bool isFirstTime = true;
        private float birthTime;
        private Dictionary<int, bool> skillUsed = new Dictionary<int, bool>();
        private Dictionary<int, float> skillLastUsedTime = new Dictionary<int, float>();
        private bool hasSetRange = false;  // 标记是否已设置过射程
        private Dictionary<int, bool> gunRangeSet = new Dictionary<int, bool>();  // 记录每个技能的射程是否已设置
        private Dictionary<int, float> gunBirthTime = new Dictionary<int, float>();  // 记录每个技能的出生时间
        private float monsterBirthTime;  // 记录怪物的出生时间
        
        /// <summary>
        /// 怪物死亡原因
        /// </summary>
        public MonsterDeathCause DeathCause { get; set; } = MonsterDeathCause.KilledByPlayer;

        public MonsterThing(object key = default, IBubbleNode parent = null) : base(key, parent)
        {
            monsterBirthTime = Time.time;  // 记录怪物的出生时间
            birthTime = Time.time;
            hasSetRange = false;
            gunRangeSet.Clear();  // 初始化时清空射程设置记录
            gunBirthTime.Clear();  // 初始化时清空出生时间记录
           ///  ///Debug.Log($"44444444 怪物在 {monsterBirthTime} 秒出生");
        }

        /// <summary>
        ///     物件类型
        /// </summary>
        public override ThingType ThingType { get; set; } = ThingType.Monster;

        /// <summary>
        ///     阵营
        /// </summary>
        public override Camp Camp { get; set; } = Camp.Monster;

        /// <summary>
        ///     怪物界面
        /// </summary>
        public MonsterBase Monster => ThingBehaviour as MonsterBase;

        /// <summary>
        ///     怪物是从哪个刷怪配置出生的
        /// </summary>
        public BattleBrushEnemy.Item CsvRow_BattleBrushEnemy
        {
            get => CsvRow_BattleBrushEnemy_m;
            set
            {
                CsvRow_BattleBrushEnemy_m = value;
                AiEventList.Clear();
                bossCGGuns.Clear();
                try
                {
                    // 处理BossCG字段的技能ID
                    string[] skillIds = CsvRow_BattleBrushEnemy_m.BossCG.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    // ///Debug.Log($"3333333 BossCG字段原始值: {CsvRow_BattleBrushEnemy_m.BossCG}");
                    if (skillIds is { Length: > 0 })
                    {
                        // ///Debug.Log($"3333333 BossCG字段解析出技能ID数量: {skillIds.Length}");
                        var validSkillIds = skillIds.ToList().ConvertAll(int.Parse).Where(id => id > 0).Distinct().ToList();
                        // ///Debug.Log($"3333333 BossCG字段有效技能ID: {string.Join(",", validSkillIds)}");
                        
                        // 加载BossCG配置的技能
                        foreach (var skillId in validSkillIds)
                        {
                            // ///Debug.Log($"3333333 正在为BossCG创建技能ID: {skillId}");
                            var gunItem = new GunItem { GunId = skillId, GunLvl = 1, StarCount = 1 };
                            (GunThing gun, _) = AddGun(gunItem, false);
                            
                            if (gun != null)
                            {
                                gun.InitFromCsv(skillId, 1, 1);
                                if (gun.CsvRow_Gun?.Value != null)
                                {
                                    gun.Camp = Camp;
                                    gun.IsHidden = true;
                                    
                                    // 从配置表读取射程值，但不修改配置表
                                    var gunConfig = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[skillId];
                                    float configRange = gunConfig.GunRange;
                                 ///    ///Debug.Log($"44444444 创建BossCG技能: {skillId}, 配置表射程: {configRange}, 当前射程: {gun.CsvRow_Gun.Value.GunRange}");
                                    
                                    // 重置射程为配置表的值，但不修改配置表
                                    if (gun.CsvRow_Gun.Value.GunRange != configRange)
                                    {
                                      ///   ///Debug.Log($"44444444 重置技能 {skillId} 的射程从 {gun.CsvRow_Gun.Value.GunRange} 到 {configRange}");
                                        
                                        // 清除射程属性
                                        var existingProps = gun.AttachedProps.Where(p => p.PropType == PropType.GunRange).ToList();
                                        foreach (var prop in existingProps)
                                        {
                                            gun.AttachedProps.Remove(prop);
                                        }
                                        
                                        // 创建一个新的射程属性
                                        var rangeProp = new CommonProp
                                        {
                                            AttachedType = AttachedType.Gun,
                                            AttachTo = new List<int> { skillId },
                                            PropCatalog = PropCatalog.Inherent,
                                            PropType = PropType.GunRange,
                                            ValueType = CsValueType.Double,
                                            ApplyType = ApplyType.Attached
                                        }.SetDouble(configRange);
                                        
                                        // 添加到附着属性列表
                                        gun.AttachedProps.Add(rangeProp);
                                        
                                        // 重置TotalProps中的射程值
                                        if (gun.TotalProps.ContainsKey(PropType.GunRange))
                                        {
                                            gun.TotalProps[PropType.GunRange].SetDouble(configRange);
                                        }
                                    }
                                    
                                    bossCGGuns.Add(gun);
                                }
                                else
                                {
                                    //Debug.LogError($"3333333 技能初始化失败，GunId: {skillId}");
                                }
                            }
                            else
                            {
                                //Debug.LogError($"3333333 创建技能失败，GunId: {skillId}");
                            }
                        }
                    }
                    
                    // 处理EventID字段的AI事件
                    if (CsvRow_BattleBrushEnemy_m.EventID > 0)
                    {
                        var aiEvent = new AiEventState
                        {
                            CsvRow_AiEvent = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<AiEventCsv>()
                                .Dic[CsvRow_BattleBrushEnemy_m.EventID]
                        };
                        // ///Debug.Log($"3333333 EventID字段AI事件: ID={aiEvent.CsvRow_AiEvent.Id}, Event类型={aiEvent.CsvRow_AiEvent.Event}, Actions={string.Join(",", aiEvent.CsvRow_AiEvent.Actions)}");
                        AiEventList.Add(aiEvent);
                    }
                }
                catch (Exception ex)
                {
                    //Debug.LogError($"3333333 处理AI事件时发生错误: {ex.Message}");
                }
            }
        }

        public List<AiEventState> AiEventList { get; } = new();

        /// <summary>
        ///     能否将属性附着于怪物
        /// </summary>
        public override bool CanAttach(CommonProp prop)
        {
            if (prop.AttachedType == AttachedType.Monster)
            {
                // AttachTo是否满足
                if (!prop.AttachTo.Contains(CsvRow_BattleBrushEnemy.Id))
                {
                    return false;
                }
            }

            // 运行到这里，就是可附着了
            return true;
        }

        #region 重新加载固有属性

        /// <inheritdoc />
        public override List<CommonProp> FindInherentPropRows()
        {
            Dictionary<int, List<CommonProp>> dic =
                SingletonMgr.Instance.GlobalMgr.GetMonsterPropIds(CsvRow_BattleBrushEnemy);
            return dic[CsvRow_BattleBrushEnemy.Id];
        }

        #endregion

        /// <inheritdoc />
        public override bool PickProps()
        {
            // 从自己的附着属性中提取
            bool rtn = base.PickProps();

            // 从玩家的附着属性中提取
            List<CommonProp> lst1 = SingletonMgr.Instance.BattleMgr.Actor.AttachedProps.FindPropsCanApplyTo(this);
            lst1.ForEach(p => AddProp(p));

            // 从玩家的枪的附着属性中提取
            List<CommonProp> lst2 = SingletonMgr.Instance.BattleMgr.Actor.Guns
                .SelectMany(g => g.AttachedProps.FindPropsCanApplyTo(this))
                .ToList();
            lst2.ForEach(p => AddProp(p));

            return rtn || lst1.Count > 0;
        }

        /// <inheritdoc />
        public override bool HasEquip(int GoodsId, int starExp = 0)
        {
            return Guns.Any(g => 
                SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[g.GunId].GoodsID == GoodsId) ||
                SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[CsvRow_BattleBrushEnemy.SkillID]
                    .GoodsID == GoodsId;
        }

        //public override bool CanAddBuff(BuffThing buff)
        //{
        //	return buff.BuffId is not (100 or 101 or 102) // 非0移动方式 不接受 定身、击退、减速
        //		;
        //	//return GetHoistedLong(PropType.MoveMethod).FirstOrDefault() == 0 || // 0移动方式 可以接受任何Buff
        //	//	   buff.BuffId is not (100 or 101 or 102) // 非0移动方式 不接受 定身、击退、减速
        //	//	;
        //}
        //

        private void Update()
        {
            UpdateSkillRange();
        }

        public void UpdateSkillRange()
        {
            foreach (var gun in bossCGGuns)  // 只遍历BossCG技能
            {
                if (gun == null || gun.CsvRow_Gun?.Value == null) continue;
                
                int skillId = gun.GunId;
                float currentTime = Time.time;
                
                // 如果还没有记录出生时间，记录它
                if (!gunBirthTime.ContainsKey(skillId))
                {
                    gunBirthTime[skillId] = currentTime;
                ///     ///Debug.Log($"44444444 记录BossCG技能 {skillId} 的出生时间: {currentTime}, 当前射程: {gun.CsvRow_Gun.Value.GunRange}");
                    continue;
                }
                
                float timeSinceBirth = currentTime - gunBirthTime[skillId];
                
                // 检查是否已经过了配置的时间
                float waitTime = (float)CsvRow_BattleBrushEnemy_m.Mass;  // 从配置表读取Mass字段的值
                
              ///   ///Debug.Log($"44444444 BossCG技能 {skillId} 当前时间: {currentTime}, 出生时间: {gunBirthTime[skillId]}, 已等待时间: {timeSinceBirth}, 配置等待时间: {waitTime}");
                
                if (timeSinceBirth >= waitTime && !gunRangeSet[skillId])
                {
                    // 解析ExploseSE字段的值
                    string[] exploseWeights = CsvRow_BattleBrushEnemy_m.ExploseSE.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    if (exploseWeights.Length != bossCGGuns.Count)
                    {
                        Debug.LogError($"44444444 ExploseSE字段参数数量 {exploseWeights.Length} 与BossCG技能数量 {bossCGGuns.Count} 不匹配");
                        continue;
                    }
                    
                    // 生成随机数，范围是0到9999
                    int randomValue = UnityEngine.Random.Range(0, 10000);
                     ///Debug.Log($"44444444 随机值范围: 0-9999, 生成随机值: {randomValue}");
                    
                    // 确定要修改的技能索引
                    int selectedSkillIndex = -1;  // 初始化为-1，表示未选中任何技能
                    int currentWeight = 0;
                    for (int i = 0; i < exploseWeights.Length; i++)
                    {
                        if (int.TryParse(exploseWeights[i], out int weight))
                        {
                            if (randomValue >= currentWeight && randomValue < currentWeight + weight)
                            {
                                selectedSkillIndex = i;
                                break;
                            }
                            currentWeight += weight;
                        }
                    }
                    
                    // 如果随机值在权重范围内，则修改对应技能的射程
                    if (selectedSkillIndex >= 0 && selectedSkillIndex < bossCGGuns.Count)
                    {
                        var selectedGun = bossCGGuns[selectedSkillIndex];
                        int selectedSkillId = selectedGun.GunId;
                        
                         ///Debug.Log($"7777777 随机值 {randomValue} 在权重范围内，选中BossCG技能 {selectedSkillId}，将射程设置为150");
                        
                        // 清除射程属性
                        var existingProps = selectedGun.AttachedProps.Where(p => p.PropType == PropType.GunRange).ToList();
                        foreach (var prop in existingProps)
                        {
                            selectedGun.AttachedProps.Remove(prop);
                        }
                        
                        // 创建一个新的射程属性
                        var rangeProp = new CommonProp
                        {
                            AttachedType = AttachedType.Gun,
                            AttachTo = new List<int> { selectedSkillId },
                            PropCatalog = PropCatalog.Inherent,
                            PropType = PropType.GunRange,
                            ValueType = CsValueType.Double,
                            ApplyType = ApplyType.Attached
                        }.SetDouble(150f);
                        
                        // 添加到附着属性列表
                        selectedGun.AttachedProps.Add(rangeProp);
                        
                        // 重置TotalProps中的射程值
                        if (selectedGun.TotalProps.ContainsKey(PropType.GunRange))
                        {
                            selectedGun.TotalProps[PropType.GunRange].SetDouble(150f);
                        }
                        
                        gunRangeSet[selectedSkillId] = true;
                        
                        // 重置所有BossCG技能的计时器
                        float resetTime = Time.time;
                        foreach (var bossGun in bossCGGuns)
                        {
                            if (bossGun != null)
                            {
                                gunRangeSet[bossGun.GunId] = false;
                                gunBirthTime[bossGun.GunId] = resetTime;
                            }
                        }
                    }
                    else
                    {
                         ///Debug.Log($"7777777 随机值 {randomValue} 不在权重范围内，本次不修改BossCG技能射程");
                    }
                }
            }
        }

        /// <inheritdoc />
        public override void ReCreateGuns(List<GunItem> gunsInBag, bool apply = false)
        {
            // ///Debug.Log($"3333333 ReCreateGuns开始执行，当前枪械数量: {Guns.Count}");
            
            // 清空现有Guns列表
            Guns.Clear();
            bossCGGuns.Clear();
            skillUsed.Clear();
            skillLastUsedTime.Clear();
            gunRangeSet.Clear();  // 清空射程设置记录
            gunBirthTime.Clear();  // 清空出生时间记录
            
            // 使用HashSet来跟踪已添加的技能ID
            var addedSkillIds = new HashSet<int>();
            
            // 处理BossCG字段的技能
            if (CsvRow_BattleBrushEnemy_m != null && !string.IsNullOrEmpty(CsvRow_BattleBrushEnemy_m.BossCG))
            {
                string[] bossCGIds = CsvRow_BattleBrushEnemy_m.BossCG.Split(";", StringSplitOptions.RemoveEmptyEntries);
                var bossCGSkillIds = bossCGIds.ToList().ConvertAll(int.Parse).Where(id => id > 0).Distinct().ToList();
                // ///Debug.Log($"3333333 BossCG字段解析出的技能ID: {string.Join(",", bossCGSkillIds)}");
                
                // 先打印所有技能的配置表射程
                foreach (var skillId in bossCGSkillIds)
                {
                    // 获取配置表数据
                    var gunConfig = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[skillId];
                     ///Debug.Log($"44444444 技能 {skillId} 在配置表中的射程: {gunConfig.GunRange}");
                }
                
                foreach (var skillId in bossCGSkillIds)
                {
                    if (addedSkillIds.Contains(skillId))
                    {
                        // ///Debug.Log($"3333333 BossCG技能 {skillId} 已经添加过，跳过");
                        continue;
                    }
                    
                    var gunItem = new GunItem { GunId = skillId, GunLvl = 1, StarCount = 1 };
                    (GunThing gun, _) = AddGun(gunItem, false);
                    
                    if (gun != null)
                    {
                        gun.InitFromCsv(skillId, 1, 1);
                        if (gun.CsvRow_Gun?.Value != null)
                        {
                            gun.Camp = Camp;
                            gun.IsHidden = true;
                            
                            // 获取配置表数据
                            var gunConfig = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[skillId];
                            float configRange = gunConfig.GunRange;
                             ///Debug.Log($"44444444 创建BossCG技能: {skillId}, 配置表射程: {configRange}, 当前射程: {gun.CsvRow_Gun.Value.GunRange}");
                            
                            // 重置射程为配置表的值，但不修改配置表
                            if (gun.CsvRow_Gun.Value.GunRange != configRange)
                            {
                                 ///Debug.Log($"44444444 重置技能 {skillId} 的射程从 {gun.CsvRow_Gun.Value.GunRange} 到 {configRange}");
                                
                                // 清除射程属性
                                var existingProps = gun.AttachedProps.Where(p => p.PropType == PropType.GunRange).ToList();
                                foreach (var prop in existingProps)
                                {
                                    gun.AttachedProps.Remove(prop);
                                }
                                
                                // 创建一个新的射程属性
                                var rangeProp = new CommonProp
                                {
                                    AttachedType = AttachedType.Gun,
                                    AttachTo = new List<int> { skillId },
                                    PropCatalog = PropCatalog.Inherent,
                                    PropType = PropType.GunRange,
                                    ValueType = CsValueType.Double,
                                    ApplyType = ApplyType.Attached
                                }.SetDouble(configRange);
                                
                                // 添加到附着属性列表
                                gun.AttachedProps.Add(rangeProp);
                                
                                // 重置TotalProps中的射程值
                                if (gun.TotalProps.ContainsKey(PropType.GunRange))
                                {
                                    gun.TotalProps[PropType.GunRange].SetDouble(configRange);
                                }
                            }
                            
                            bossCGGuns.Add(gun);
                            Guns.Add(gun);
                            addedSkillIds.Add(skillId);
                            gunRangeSet[skillId] = false;  // 初始化射程设置状态
                        }
                        else
                        {
                            Debug.LogError($"3333333 技能初始化失败，GunId: {skillId}");
                        }
                    }
                }
            }

            // 停止已停用的枪械
            Guns.Where(g => g.IsHidden).ToList().ForEach(g => g.StopCdExecutor());
            
            // 为SkillID创建隐藏枪械
            gunsInBag.Where(g => g.GunId > 0).ToList().ForEach(g =>
            {
                if (g.GunLvl <= 0) g.GunLvl = 1;
                if (g.StarCount <= 0) g.StarCount = 1;
                
                if (addedSkillIds.Contains(g.GunId))
                {
                    // ///Debug.Log($"3333333 SkillID技能 {g.GunId} 已经添加过，跳过");
                    return;
                }
                
                (GunThing gun, _) = AddGun(g, false);
                
                if (gun != null)
                {
                    gun.InitFromCsv(g.GunId, g.GunLvl, g.StarCount);
                    if (gun.CsvRow_Gun?.Value != null)
                    {
                        gun.Camp = Camp;
                        gun.IsHidden = true;
                        Guns.Add(gun);
                        addedSkillIds.Add(g.GunId);
                        // ///Debug.Log($"3333333 成功添加SkillID技能 {g.GunId}, 射程: {gun.CsvRow_Gun.Value.GunRange}");
                    }
                    else
                    {
                        Debug.LogError($"3333333 SkillID枪械初始化失败，GunId: {g.GunId}");
                    }
                }
            });
            
            // 最后再次检查并移除重复的枪械
            var uniqueGuns = new HashSet<int>();
            Guns.RemoveAll(gun =>
            {
                if (uniqueGuns.Contains(gun.GunId))
                {
                    // ///Debug.Log($"3333333 发现重复枪械 {gun.GunId}，已移除");
                    return true;
                }
                uniqueGuns.Add(gun.GunId);
                return false;
            });
            
            // ///Debug.Log($"3333333 ReCreateGuns执行完毕，最终枪械数量: {Guns.Count}，枪械ID列表: {string.Join(",", Guns.Select(g => g.GunId))}");
        }

        protected override void Hp_Changed(ChangeEventArgs e)
        {
            base.Hp_Changed(e);

            double originalValue = e.OriginalValue is double vo ? vo : 0;
            double newValue = e.NewValue is double vn ? vn : 0;
            double inc = newValue - originalValue;
            //玩家吸血
            if (inc < 0)
            {
                double playerHpAbsorb = SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.HpAbsorb)
                    .FirstOrDefault();
                if (playerHpAbsorb > 0)
                {
                    // var suck = System.Math.Floor(playerHpAbsorb * -inc);
                    double nActorHP = SingletonMgr.Instance.BattleMgr.Actor.Hp.Value + playerHpAbsorb;
                    nActorHP = Math.Clamp(nActorHP, SingletonMgr.Instance.BattleMgr.Actor.Hp.Value,
                        SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                    SingletonMgr.Instance.BattleMgr.Actor.Hp.Value = nActorHP;
                }
            }
        }

        /// <inheritdoc />
        public override bool TakeHit(ThingBase attacker, double damage, bool isCritical = false)
        {
            bool rtn = base.TakeHit(attacker, damage, isCritical);

            _ = AiEventList.Where(x =>
                x.CanTakeHitRebirth && x.LastTime_TakeHitRebirth + (x.CsvRow_AiEvent.EventParameter / 1000f) <
                Time.time).Select(x =>
            {
                x.LastTime_TakeHitRebirth = Time.time;
                int mId = x.GetRandomRebirthId();
                if (mId > 0)
                {
                    SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.BornMonster(Position, 3,
                        new List<int> { mId }, new List<int> { 1 });
                }

                return x;
            }).ToList();

            if (rtn)
            {
                // 奖励玩家击杀后Buff
                SingletonMgr.Instance.BattleMgr.Actor.ReceiveBuffByKillEnemy(attacker);
            }

            return rtn;
        }

        /// <inheritdoc />
        public override BulletThing CreateBullet(ThingCdExecutor shooter, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, float angle, int penetrateTimes, int bounceTimes, int separateTimes,
            ICollection<MonsterThing> monsters = null)
        {
            // 确保有可用的枪械
            if (Guns.Count == 0)
            {
                Debug.LogError($"22222222 没有可用的枪械");
                return null;
            }
            
            // 获取当前枪械
            GunThing selectedGun = null;
            
            // 当枪械不为空时，优先使用BossCG技能
            if (shooter != null && Guns.Count > 0)
            {
                // 优先使用BossCG技能
                if (bossCGGuns.Count > 0)
                {
                    // 随机选择一个BossCG技能
                    int randomIndex = UnityEngine.Random.Range(0, bossCGGuns.Count);
                    selectedGun = bossCGGuns[randomIndex];
                     ///Debug.Log($"44444444 选择BossCG技能: {selectedGun.GunId}, 射程: {selectedGun.CsvRow_Gun?.Value?.GunRange}");
                    
                    // 检查目标是否在攻击范围内
                    if (attackBaseDirFollowThing != null)
                    {
                        float gunRange = (float)selectedGun.GetTotalDouble(PropType.GunRange).FirstOrDefault();
                        float distance = attackBaseDirFollowThing.Position.CalcDistance2D_SolidCircleToSolidCircle(
                            attackBaseDirFollowThing.TotalProp_Radius,
                            Position,
                            TotalProp_Radius);
                            
                        if (distance > gunRange)
                        {
                             ///Debug.Log($"7777777 BossCG技能 {selectedGun.GunId} 目标不在攻击范围内，距离: {distance}, 射程: {gunRange}");
                            return null;
                        }
                    }
                    
                    // 使用技能后重置射程为配置表的值
                    if (selectedGun != null && selectedGun.CsvRow_Gun?.Value != null)
                    {
                        // 重置所有BossCG技能的射程
                        foreach (var gun in bossCGGuns)
                        {
                            if (gun == null || gun.CsvRow_Gun?.Value == null) continue;
                            
                            // 获取配置表数据
                            var gunConfig = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[gun.GunId];
                            float configRange = gunConfig.GunRange;
                            
                            // 清除射程属性
                            var existingProps = gun.AttachedProps.Where(p => p.PropType == PropType.GunRange).ToList();
                            foreach (var prop in existingProps)
                            {
                                gun.AttachedProps.Remove(prop);
                            }
                            
                            // 创建一个新的射程属性
                            var rangeProp = new CommonProp
                            {
                                AttachedType = AttachedType.Gun,
                                AttachTo = new List<int> { gun.GunId },
                                PropCatalog = PropCatalog.Inherent,
                                PropType = PropType.GunRange,
                                ValueType = CsValueType.Double,
                                ApplyType = ApplyType.Attached
                            }.SetDouble(configRange);
                            
                            // 添加到附着属性列表
                            gun.AttachedProps.Add(rangeProp);
                            
                            // 重置TotalProps中的射程值
                            if (gun.TotalProps.ContainsKey(PropType.GunRange))
                            {
                                gun.TotalProps[PropType.GunRange].SetDouble(configRange);
                            }
                        }
                        
                         ///Debug.Log($"7777777 使用BossCG技能 {selectedGun.GunId} 后，所有BossCG技能射程重置为配置表值");
                        
                        // 打印所有BossCG技能的射程
                        string bossCGRanges = string.Join(", ", bossCGGuns.Select(gun => 
                            $"技能ID:{gun.GunId} 射程:{gun.GetTotalDouble(PropType.GunRange).FirstOrDefault()}"));
                         ///Debug.Log($"7777777 当前所有BossCG技能射程: {bossCGRanges}");
                    }
                }
                else
                {
                    // 如果没有BossCG技能，则随机选择其他技能
                    int randomIndex = UnityEngine.Random.Range(0, Guns.Count);
                    selectedGun = Guns[randomIndex];
                     ///Debug.Log($"44444444 选择普通技能: {selectedGun.GunId}, 射程: {selectedGun.CsvRow_Gun?.Value?.GunRange}");
                }
                
                // 确保枪械已初始化
                if (selectedGun.CsvRow_Gun?.Value == null)
                {
                    Debug.LogError($"22222222 枪械未正确初始化，尝试重新初始化 GunId: {selectedGun.GunId}");
                    selectedGun.InitFromCsv(selectedGun.GunId, 1, 1);
                }
            }
            
            BulletThing rtn = base.CreateBullet(shooter, attackBaseDirFollowThing, trackPos, angle, penetrateTimes,
                bounceTimes, separateTimes, monsters);
            
            if (rtn != null)
            {
                // ///Debug.Log($"44444444 成功创建子弹，使用技能: {selectedGun?.GunId}, 射程: {selectedGun?.CsvRow_Gun?.Value?.GunRange}");
            }
            else
            {
                Debug.LogError($"3333333 创建子弹失败，使用技能: {selectedGun?.GunId}");
            }

            BulletType bulletType = (BulletType)(int)rtn.GetTotalLong(PropType.BulletType).FirstOrDefault();
            if (bulletType == BulletType.Bullet)
            {
                rtn.AttackBaseDirFollowThing = attackBaseDirFollowThing;
            }
            else if (bulletType == BulletType.MissileTrackPos)
            {
                rtn.TrackPosition = trackPos;
                if (angle > 0 && trackPos.HasValue)
                {
                    // 绕起点旋转
                    rtn.TrackPosition = trackPos.Value.RotateAround(Vector3.forward, angle, Position);
                }
            }

            return rtn;
        }

        /// <summary>
        /// 延迟0.1秒后重置枪械射程为配置表的值
        /// </summary>
        private async UniTaskVoid ResetGunRangeAfterDelay(GunThing gun)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(0.1f));
            
            if (gun == null || gun.CsvRow_Gun?.Value == null) return;
            
            // 获取配置表数据
            var gunConfig = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[gun.GunId];
            float configRange = gunConfig.GunRange;
            
             ///Debug.Log($"44444444 重置BossCG技能 {gun.GunId} 的射程从 {gun.CsvRow_Gun.Value.GunRange} 到 {configRange}");
            
            // 清除射程属性
            var existingProps = gun.AttachedProps.Where(p => p.PropType == PropType.GunRange).ToList();
            foreach (var prop in existingProps)
            {
                gun.AttachedProps.Remove(prop);
            }
            
            // 创建一个新的射程属性
            var rangeProp = new CommonProp
            {
                AttachedType = AttachedType.Gun,
                AttachTo = new List<int> { gun.GunId },
                PropCatalog = PropCatalog.Inherent,
                PropType = PropType.GunRange,
                ValueType = CsValueType.Double,
                ApplyType = ApplyType.Attached
            }.SetDouble(configRange);
            
            // 添加到附着属性列表
            gun.AttachedProps.Add(rangeProp);
            
            // 重置TotalProps中的射程值
            if (gun.TotalProps.ContainsKey(PropType.GunRange))
            {
                gun.TotalProps[PropType.GunRange].SetDouble(configRange);
            }
            
            // 重置所有BOSSCG技能的计时器
            float currentTime = Time.time;
            foreach (var bossGun in bossCGGuns)
            {
                if (bossGun != null)
                {
                    gunRangeSet[bossGun.GunId] = false;
                    gunBirthTime[bossGun.GunId] = currentTime;
                }
            }
        }
    }
}