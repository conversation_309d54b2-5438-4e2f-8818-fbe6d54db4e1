﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;
using Apq.Utils;

using CsvTables;

using Props;

using ThingCdExecutors;

using UnityEngine;

using View;

using X.PB;

namespace Thing
{
    /// <summary>
    /// 子弹物件(数据)
    /// </summary>
    /// <remarks>
    /// 数据初始化顺序：
    /// Sender
    /// CsvRow_Bullet
    /// 
    /// 固有属性
    /// </remarks>
    public class BulletThing : ThingBase
    {
        /// <summary>
        /// 物件类型
        /// </summary>
        public override ThingType ThingType { get; set; } = ThingType.Bullet;

        /// <summary>
        /// 子弹界面
        /// </summary>
        public BulletBase BulletBehaviour => ThingBehaviour as BulletBase;

        /// <summary>
        /// 子弹配置行
        /// </summary>
        public BubblingList<BulletCfg.Types.CSVRow> CsvRow_Bullet { get; }

        /// <summary>
        /// 该子弹是哪个执行者发射的
        /// </summary>
        public ThingCdExecutor CdExecutor { get; set; }
        
        /// <summary>
        /// 攻击基准方向跟随哪个物件(没值则取移动方向)
        /// </summary>
        public ThingBase AttackBaseDirFollowThing { get; set; }

        /// <summary>
        /// 子弹方向与攻击基准方向的角度
        /// </summary>
        public float Angle { get; set; }

        /// <summary>
        /// 子弹的追踪位置(有值就是定点攻击)
        /// </summary>
        public Vector3? TrackPosition { get; set; }

        /// <summary>
        /// 子弹发射时角色的移动时长
        /// </summary>
        public float MoveDuration { get; set; }

        /// <summary>
        /// 分裂出生时，上个子弹击中了哪些怪物(该子弹不再攻击这些怪物)
        /// </summary>
        public BubblingList<MonsterThing> SeparateFrom { get; }

        /// <summary>
        /// 子弹物件(数据)
        /// </summary>
        public BulletThing(object key = default, IBubbleNode parent = null) : base(key, parent)
        {
            CsvRow_Bullet = new(nameof(CsvRow_Bullet), this);
            LifeBeginTime = new(nameof(LifeBeginTime), this);
            NextHitEnemyTime = new(nameof(NextHitEnemyTime), this);
            PenetrateTimes = new(nameof(PenetrateTimes), this);
            BounceTimes = new(nameof(BounceTimes), this);
            SeparateTimes = new(nameof(SeparateTimes), this);
            SeparateNo = new(nameof(SeparateNo), this);
            ShouldDiscard = new(nameof(ShouldDiscard), this);
            SeparateFrom = new(nameof(SeparateFrom), this);
        }

        #region 读取配置

        /// <summary>
        /// 初始化(读取csv表格中的一行)
        /// </summary>
        public BulletThing InitFromCsv(int bulletId, int bulletLvl)
        {
            CsvRow_Bullet.Value =
                SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().GetByBulletId(bulletId)[bulletLvl];
            return this;
        }

        #endregion

        /// <summary>
        /// 能否将属性附着于子弹
        /// </summary>
        public override bool CanAttach(CommonProp prop)
        {
            if (prop.AttachedType != AttachedType.Bullet) return false;

            // AttachTo是否满足
            if (!prop.AttachTo.Contains(CsvRow_Bullet.Value.Id)) return false;

            // 此子弹是玩家阵营的子弹
            if (CdExecutor.Thing.Camp == Camp.Player && CdExecutor.Thing is GunThing gun)
            {
                // 枪的武器等级没达标
                if (gun.WeaponLvl.Value + 2 < prop.AttachPremissMinWeaponLvl) return false;

                // 枪的等级没达标
                if (gun.ThingLvl.Value < prop.AttachPremissMinGunLvl) return false;

                if (prop.AttachPremiseGoodsID > 0)
                {
                    // 没有达标的装备
                    if (!gun.Actor.HasEquip(prop.AttachPremiseGoodsID, prop.AttachPremiseMinStarExp)) return false;
                }
            }

            // 运行到这里，就是可附着了
            return true;
        }

        /// <summary>
        /// 能否将属性应用于子弹
        /// </summary>
        public override bool CanApply(CommonProp prop)
        {
            if (prop.AttachedThing is BuffThing buff)
            {
                // 不是Buff的承受者
                if (prop.ApplyType == ApplyType.BuffBearer && Util.IsEquals(buff.Bearer, this)) return false;
            }

            if (prop.ApplyType == ApplyType.Attached)
            {
                // 不是被附者
                if (!Util.IsEquals(prop.AttachedThing, this)) return false;
            }
            else
            {
                // ApplyTo是否满足
                var isApplyToMatched = prop.ApplyType switch
                {
                    ApplyType.Any => true,
                    ApplyType.BulletId => prop.ApplyTo.Contains(CsvRow_Bullet.Value.Id),
                    ApplyType.BulletCatalog => prop.ApplyTo.Contains(CsvRow_Bullet.Value.BulletCatalog),
                    ApplyType.BulletAll => true,
                    _ => false,
                };
                if (!isApplyToMatched) return false;
            }

            // 此子弹是玩家阵营的子弹
            if (CdExecutor.Thing.Camp == Camp.Player && CdExecutor.Thing is GunThing gun)
            {
                // 枪的武器等级没达标
                if (gun.WeaponLvl.Value + 2 < prop.ApplyPremiseMinWeaponLvl) return false;

                // 枪的等级没达标
                if (gun.ThingLvl.Value < prop.ApplyPremiseMinGunLvl) return false;

                if (prop.ApplyPremiseGoodsID > 0)
                {
                    // 没有达标的装备
                    if (!gun.Actor.HasEquip(prop.ApplyPremiseGoodsID, prop.ApplyPremiseMinStarExp)) return false;
                }
            }

            // 运行到这里，就是可应用了
            return true;
        }

        /// <inheritdoc/>
        public override List<CommonProp> FindInherentPropRows()
        {
            var rtn = new List<CommonProp>();
            if (!AttachedProps.HasValue)
            {
                // 将CsvRow_Bullet的属性 转为通用属性
                rtn.AddRange(new List<CommonProp>
                {
                    // 阵营
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Camp,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((long)CdExecutor.Thing.Camp),
                    // 移动方法
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MoveMethod,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((int)MoveMethod.PositionMove),
                    // 移动方向
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.HowMoveDir,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((int)DirMethod.Straight),
                    // 子弹类型
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.BulletType,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((int)CsvRow_Bullet.Value.BulletType),
                    // 子弹图片
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Img,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetString(CsvRow_Bullet.Value.Img),
                    // 半径(取自发射子弹的物件)
                    new CommonProp
                    {
                        AttachedType = AttachedType.Bullet,
                        AttachTo = new List<int> { CsvRow_Bullet.Value.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Radius,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(CdExecutor.Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault()),
                });
            }

            return rtn;
        }

        /// <summary>
        /// 获取子弹的视图类
        /// </summary>
        public Type GetBulletViewClass()
        {
            var bulletType = (BulletType)(int)GetTotalLong(PropType.BulletType).FirstOrDefault();
            if (Camp == Camp.Player)
            {
                return bulletType switch
                {
                    BulletType.Bullet => typeof(ActorLocusBullet),
                    BulletType.MissileTrackPos => typeof(ActorMissileTrackPos),
                    _ => typeof(ActorLocusBullet),
                };
            }

            if (Camp == Camp.Monster)
            {
                return bulletType switch
                {
                    BulletType.MissileTrackPos => typeof(MonsterMissileTrackPos),
                    BulletType.SiegeBomb => typeof(MonsterSiegeBomb),
                    _ => typeof(MonsterLocusBullet),
                };
            }

            return null;
        }

        /// <summary>
        /// 子弹生命开始时间
        /// </summary>
        public BubblingList<float> LifeBeginTime { get; }

        /// <summary>
        /// 下次可以击中敌人的时间
        /// </summary>
        public BubblingList<float> NextHitEnemyTime { get; }

        /// <summary>
        /// 剩余穿透次数
        /// </summary>
        public BubblingList<int> PenetrateTimes { get; }

        /// <summary>
        /// 剩余反弹次数
        /// </summary>
        public BubblingList<int> BounceTimes { get; }

        /// <summary>
        /// 剩余分裂次数
        /// </summary>
        public BubblingList<int> SeparateTimes { get; }

        /// <summary>
        /// 属于第几次分裂产生的子弹(0开始)
        /// </summary>
        public BubblingList<int> SeparateNo { get; }

        /// <summary>
        /// 是否应该丢弃该子弹
        /// </summary>
        public BubblingList<bool> ShouldDiscard { get; }
    }
}