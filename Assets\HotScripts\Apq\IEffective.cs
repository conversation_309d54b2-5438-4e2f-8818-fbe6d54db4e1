﻿using System;

namespace Apq
{
	/// <summary>
	/// 提供生效期支持的接口
	/// </summary>
	public interface IEffective
	{
        /// <summary>
        /// 提供当前时间
        /// </summary>
        public Func<DateTime> NowProvider { get; set; }

        /// <summary>
        /// 失效模式{0:总是生效,1:起止时间区间}
        /// </summary>
        public int InvalidMode { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 是否太早(还未到生效期)
        /// </summary>
        public bool IsTooEarly();

        /// <summary>
        /// 是否太晚(已过期)
        /// </summary>
        public bool IsTooLate();

        /// <summary>
        /// 是否已生效
        /// </summary>
        public bool IsEffective();
    }
}
