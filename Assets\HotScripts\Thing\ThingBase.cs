﻿// ReSharper disable InconsistentNaming
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.ChangeBubbling;
using Apq.Extension;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using ThingCdExecutors;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace Thing
{
    /// <summary>
    ///     物件基类
    /// </summary>
    public abstract partial class ThingBase : BubblingDic<PropType, CommonProp>, IDisposable
    {
        protected ThingBase(object key = default, IBubbleNode parent = null)
        {
            Parent = parent;
            Key = key;

            Area2D = new BubblingList<IArea2D>(nameof(Area2D), this);
            ThingLvl = new BubblingList<int>(nameof(ThingLvl), this);
            Hp = new BubblingList<double>(nameof(Hp), this);
            Armor = new BubblingList<double>(nameof(Armor), this);
            MoveDirection_Straight = new BubblingList<Vector3>(nameof(MoveDirection_Straight), this);
            // MapCols = new(nameof(MapCols), this);
            RotationTowards = new BubblingList<Vector3>(nameof(RotationTowards), this);
            AttachedProps = new BubblingList<CommonProp>(nameof(AttachedProps), this);
            HoistedProps = new BubblingList<HoistedProp>(nameof(HoistedProps), this);
            TotalProps = new BubblingDic<PropType, AggProp>(nameof(TotalProps), this);
            CarriedBuffs_HitForInjurer = new BubblingList<BuffThing>(nameof(CarriedBuffs_HitForInjurer), this);
            CarriedBuffs_KilledForAttacker = new BubblingList<BuffThing>(nameof(CarriedBuffs_KilledForAttacker), this);
            Guns = new BubblingList<GunThing>(nameof(Guns), this);
            Buffs = new BubblingList<BuffThing>(nameof(Buffs), this);
            LockedByShooters = new BubblingList<ThingCdExecutor>(nameof(LockedByShooters), this);
            LockedByBullets = new BubblingList<BulletThing>(nameof(LockedByBullets), this);

            ThingLvl.Value = 1;

            Hp.Changed += Hp_Changed;
            Armor.Changed += Armor_Changed;
            ThingLvl.Changed += ThingLvl_Changed;
        }

        /// <summary>
        ///     物件类型
        /// </summary>
        public virtual ThingType ThingType { get; set; }

        /// <summary>
        ///     物件所属阵营
        /// </summary>
        public virtual Camp Camp { get; set; }

        /// <summary>
        ///     物件的唯一ID ( Clone/CopyTo 不包含此属性 )
        /// </summary>
        public Guid Guid { get; set; } = Guid.NewGuid();

        /// <summary>
        ///     所有者
        /// </summary>
        public ThingBase Owner { get; set; }

        /// <summary>
        ///     物件等级
        /// </summary>
        public BubblingList<int> ThingLvl { get; set; }

        /// <summary>
        ///     当前血量
        /// </summary>
        public BubblingList<double> Hp { get; }

        /// <summary>
        ///     当前护甲
        /// </summary>
        public BubblingList<double> Armor { get; }

        /// <summary>
        ///     物件占用的背包格子坐标(背包坐标系)
        /// </summary>
        /// <remarks>不Clone此属性</remarks>
        public List<BagPosition> PosInBag { get; } = new();

        /// <summary>
        ///     直线运动方向(归一化)
        /// </summary>
        public BubblingList<Vector3> MoveDirection_Straight { get; }

        /// <summary>
        ///     自转朝向
        /// </summary>
        public BubblingList<Vector3> RotationTowards { get; }

        /// <summary>
        ///     已复活次数
        /// </summary>
        public int ReviveCount { get; set; }

        /// <summary>
        ///     获取半径
        /// </summary>
        public float TotalProp_Radius => (float)(GetTotalDouble(PropType.Radius)?.FirstOrDefault() ?? 1.0f);

        /// <summary>
        ///     获取最大血量
        /// </summary>
        public double TotalProp_MaxHp => GetTotalDouble(PropType.MaxHp)?.FirstOrDefault() ?? 100.0;

        /// <summary>
        ///     获取射程
        /// </summary>
        public float TotalProp_GunRange => (float)(GetTotalDouble(PropType.GunRange)?.FirstOrDefault() ?? 5.0f);

        /// <summary>
        ///     是否被冰冻
        /// </summary>
        public bool IsFreeze { get; set; }

        protected virtual void ThingLvl_Changed(ChangeEventArgs e)
        {
        }

        protected virtual void Hp_Changed(ChangeEventArgs e)
        {
            // 物件血量变化
            MessageBroker.Default.Publish(new ThingHp
            {
                Thing = this,
                OriginalValue = e.OriginalValue is double originalValue ? originalValue : 0,
                NewValue = e.NewValue is double newValue ? newValue : 0
            });

            // Hp变成很小的值时视为死亡
            if (Hp.Value <= float.Epsilon)
            {
                MessageBroker.Default.Publish(new ThingDead { Thing = this });
            }
        }

        protected virtual void Armor_Changed(ChangeEventArgs e)
        {
            // 物件护甲变化
            MessageBroker.Default.Publish(new ThingArmor
            {
                Thing = this,
                OriginalValue = e.OriginalValue is double originalValue ? originalValue : 0,
                NewValue = e.NewValue is double newValue ? newValue : 0
            });
        }

        /// <summary>
        /// 发布消息:物件死亡
        /// </summary>
        public virtual async UniTaskVoid PublishMsg_ThingDead(float delay)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(delay));
            
            MessageBroker.Default.Publish(new ThingDead { Thing = this });
        }

        /// <summary>
        ///     是否拥有指定的装备且升星经验达标
        /// </summary>
        public virtual bool HasEquip(int GoodsId, int starExp = 0)
        {
            return false;
        }

        /// <summary>
        ///     能否将属性附着于该物件
        /// </summary>
        public abstract bool CanAttach(CommonProp prop);

        /// <summary>
        ///     能否将属性应用于该物件
        /// </summary>
        /// <remarks>基类只判断了是否应用于被附者(没其它限制)和Buff承受者</remarks>
        public virtual bool CanApply(CommonProp prop)
        {
            // 附着在Buff上的属性,应用于其承受者
            if (prop.AttachedThing is BuffThing buff &&
                prop.ApplyType == ApplyType.BuffBearer && buff.Bearer == this)
            {
                return true;
            }

            // 应用于附着者
            if (prop.ApplyType == ApplyType.Attached)
            {
                if (Util.IsEquals(prop.AttachedThing, this))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///     添加附着的属性(已存在则不加)(克隆)
        /// </summary>
        public virtual CommonProp AddAttachedProp(CommonProp prop)
        {
            CommonProp c = AttachedProps.FirstOrDefault(p => p.Guid == prop.Guid);
            if (c != null)
            {
                return c;
            }

            c = (CommonProp)prop.Clone();
            c.Parent = AttachedProps;
            c.AttachedThing = this;

            AttachedProps.Add(c);
            return c;
        }

        /// <summary>
        ///     按物件配置的方法找出一个敌人
        /// </summary>
        public virtual DistanceThing FindEnemy()
        {
            return new DistanceThing { Thing1 = this, Distance = float.PositiveInfinity };
        }

        /// <summary>
        ///     接受伤害
        /// </summary>
        /// <returns>是否死亡</returns>
        public virtual bool TakeHit(ThingBase attacker, double damage, bool isCritical = false)
        {
            double maxHp = GetTotalDouble(PropType.MaxHp).FirstOrDefault();

            // 先减护甲
            double newArmor = Armor.Value - damage;
            newArmor = Math.Clamp(newArmor, 0, maxHp);
            // 护甲已抵扣的伤害
            double armorReduce = Armor.Value - newArmor;
            Armor.Value = newArmor;

            // 再减血量
            double hpReduce = damage - armorReduce;
            if (hpReduce > 0)
            {
                double oldHp = Hp.Value;
                double newHp = Hp.Value - hpReduce;
                newHp = Math.Clamp(newHp, 0, maxHp);
                hpReduce = oldHp - newHp;
                Hp.Value = newHp;
            }

            MessageBroker.Default.Publish(new HitThingCells
            {
                Thing = this, IsCritical = isCritical, Inc_Armor = armorReduce, Inc_Hp = hpReduce
                // Cells = cells
            });

            return Hp.Value <= float.Epsilon;
        }

        /// <summary>
        ///     创建子弹(数据)
        /// </summary>
        /// <param name="shooter">执行器</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="angle">子弹方向与攻击基准方向的角度</param>
        /// <param name="penetrateTimes">剩余穿透次数</param>
        /// <param name="bounceTimes">剩余反弹次数</param>
        /// <param name="separateTimes">剩余分裂次数</param>
        /// <param name="monsters">分裂出生时，上个子弹击中了哪些怪物(该子弹不再攻击这些怪物)</param>
        public virtual BulletThing CreateBullet(ThingCdExecutor shooter, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, float angle, int penetrateTimes, int bounceTimes, int separateTimes,
            ICollection<MonsterThing> monsters = null
        )
        {
            BulletThing bullet = new BulletThing("子弹") { CdExecutor = shooter, Camp = shooter.Thing.Camp }.InitFromCsv(
                (int)shooter.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault(),
                ThingLvl.Value);
            bullet.Angle = angle;
            bullet.PenetrateTimes.Value = penetrateTimes;
            bullet.BounceTimes.Value = bounceTimes;
            bullet.SeparateTimes.Value = separateTimes;
            bullet.SeparateFrom.AddRange(monsters);

            // 加载子弹附着的属性(含战场属性)
            bullet.ReloadAttachedProps();

            // 子弹提取属性
            bullet.PickProps();

            // 子弹计算提升
            _ = bullet.HoistedProps.Select(h => h.ReHoist()).ToList();

            // 子弹计算总属性(含携带的Buff，但实际不会携带Buff)
            bullet.ReCalcTotalProps();
            return bullet;
        }

        // /// <summary>
        // /// 在地图上占据哪些列(背包坐标系)
        // /// </summary>
        // public BubblingList<int> MapCols { get; }

        #region 作为攻击目标时

        /// <summary>
        ///     成为了哪些执行者的攻击目标
        /// </summary>
        public BubblingList<ThingCdExecutor> LockedByShooters { get; }

        /// <summary>
        ///     成为了哪些子弹的攻击目标
        /// </summary>
        public BubblingList<BulletThing> LockedByBullets { get; }

        #endregion

        #region IDisposable

        protected bool disposedValue;

        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposedValue)
            {
                return;
            }

            if (disposing)
            {
                Hp.Changed -= Hp_Changed;
                Armor.Changed -= Armor_Changed;
                ThingLvl.Changed -= ThingLvl_Changed;
                StopCdExecutor();
            }

            // 释放未托管的资源(未托管的对象)并重写终结器
            // 将大型字段设置为 null
            disposedValue = true;
        }

        // // TODO: 仅当"Dispose(bool disposing)"拥有用于释放未托管资源的代码时才替代终结器
        // ~PersistentClient()
        // {
        //     // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
        //     Dispose(false);
        // }

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion

        #region 界面

        /// <summary>
        ///     物件关联的界面组件
        /// </summary>
        public ThingBehaviour ThingBehaviour { get; set; }

        /// <summary>
        ///     初始化界面前
        /// </summary>
        public Action<ThingBehaviour> Provider_BeforeInitView { get; set; }

        /// <summary>
        ///     初始化界面后
        /// </summary>
        public Action<ThingBehaviour> Provider_AfterInitView { get; set; }

        /// <summary>
        ///     物件位置(世界坐标)
        /// </summary>
        public Vector3 Position { get; set; }

        /// <summary>
        ///     物件初始位置
        /// </summary>
        public Vector3 PositionInit { get; set; }

        /// <summary>
        ///     物件在界面上覆盖的区域
        /// </summary>
        public BubblingList<IArea2D> Area2D { get; }

        /// <summary>
        ///     是否碰到某个点
        /// </summary>
        public virtual bool CheckCollision(Vector2 P1)
        {
            return Vector2.Distance(P1, Position) < TotalProp_Radius;
        }

        /// <summary>
        ///     是否碰到圆形区域
        /// </summary>
        public virtual bool CheckCollision(Vector2 P1, float radius)
        {
            float dis = Position.CalcDistance2D_SolidCircleToSolidCircle(
                TotalProp_Radius, P1, radius);
            return dis <= 0;
        }

        /// <summary>
        ///     计算出生位置(依次替补)
        /// </summary>
        public virtual Vector3 CalcPositionInit()
        {
            // return PositionInit.FirstOrDefault();
            return PositionInit;
        }

        #endregion

        #region Executor

        /// <summary>
        ///     任务的取消令牌:按Cd时长的循环
        /// </summary>
        public CancellationTokenSource CTS_CdExecutor { get; set; } = new();

        /// <summary>
        ///     启动执行者:按Cd时长的循环
        /// </summary>
        /// <param name="ms">延迟启动的毫秒数</param>
        public async UniTaskVoid StartCdExecutor(int ms = 0)
        {
            CTS_CdExecutor.Cancel();
            await UniTask.NextFrame();
            CTS_CdExecutor = new CancellationTokenSource();
            await UniTask.Delay(ms);

            Task_CdExecutor(CTS_CdExecutor.Token).Forget();
        }

        /// <summary>
        ///     停止执行者:按Cd时长的循环
        /// </summary>
        public virtual void StopCdExecutor()
        {
            CTS_CdExecutor.Cancel();
        }

        /// <summary>
        ///     是否暂停 按Cd时长的循环
        /// </summary>
        public bool SuspendCdExecutor { get; set; }

        /// <summary>
        ///     任务实现:按Cd时长的循环(射击、回血、回护甲)
        /// </summary>
        /// <param name="token">CTS_CdExecutor.Token</param>
        protected virtual async UniTaskVoid Task_CdExecutor(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        break;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    // 被暂停了，不能干活
                    if (SuspendCdExecutor)
                    {
                        continue;
                    }

                    // 被冰冻了，不能干活
                    if (IsFreeze)
                    {
                        continue;
                    }

                    double cd = GetTotalDouble(PropType.Cd).FirstOrDefault();
                    if (this is GunThing { Actor: not null } gun)
                    {
                        // 角色的血量比例、在血量梯度中的索引位置
                        double hpPct = gun.Actor.Hp.Value / gun.Actor.TotalProp_MaxHp;
                        int idx = gun.Actor.GetTotalDouble(PropType.HpPctGradientList).Where(x => x >= hpPct)
                            .Select((_, i) => i).LastOrDefault();

                        cd *= 1 + gun.GetTotalDouble(PropType.CdPctGradientList).IndexOfOrFirstOrDefault(idx);

                        // 移动时长在梯度中的索引位置
                        PlayerMove playerMove = gun.Actor.ThingBehaviour.GetComponent<PlayerMove>();
                        idx = gun.GetTotalDouble(PropType.MoveDurationGradientList)
                            .Where(x => x <= playerMove.MoveDuration)
                            .Select((_, i) => i).LastOrDefault();
                        cd *= 1 + gun.GetTotalDouble(PropType.CdPctGradientListByMoveDuration).IndexOfOrFirstOrDefault(idx);
                    }

                    double minCd = GetTotalDouble(PropType.MinCd).FirstOrDefault();
                    if (cd < minCd)
                    {
                        cd = minCd;
                    }

                    // CD时长太小就拒绝执行
                    if (cd <= float.Epsilon)
                    {
                        continue;
                    }

                    // 启动Cd计时
                    StartCdTiming(cd, token).Forget();

                    // 开始干活
                    DoCdExecutor(token);
                    if (token.IsCancellationRequested)
                    {
                        break;
                    }

                    await UniTask.Delay(TimeSpan.FromSeconds(cd), cancellationToken: token);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        ///// <summary>
        ///// Cd计时是否已暂停
        ///// </summary>
        //protected bool CdPause { get; set; }

        /// <summary>
        ///     启动Cd计时
        /// </summary>
        protected async UniTaskVoid StartCdTiming(double cdMax, CancellationToken token)
        {
            MessageBroker.Default.Publish(new ThingCd { Thing = this, CdMax = cdMax, Cd = 0 });
            await UniTask.SwitchToMainThread();

            //try
            //{
            //	double cd = 0;
            //	for (; cd <= cdMax; await UniTask.NextFrame())
            //	{
            //		if (token.IsCancellationRequested) break;
            //		if (CdPause) continue;

            //		MessageBroker.Default.Publish(new ThingCd
            //		{
            //			Thing = this,
            //			CdMax = cdMax,
            //			Cd = cd,
            //		});

            //		cd += Time.deltaTime;
            //	}
            //}
            //catch (OperationCanceledException)
            //{
            //	throw;
            //}
            //catch (MissingReferenceException)
            //{
            //	// ignore
            //}
            //catch (Exception ex)
            //{
            //	Debug.LogException(ex);
            //}
        }

        /// <summary>
        ///     按Cd时长循环的执行者每轮干的活
        /// </summary>
        protected virtual void DoCdExecutor(CancellationToken token)
        {
            // 物件已死
            if (Hp.Value <= float.Epsilon)
            {
                // 结束调度
                CTS_CdExecutor.Cancel();
                return;
            }

            // 创建执行者并启动
            ThingCdExecutor shooter = CreateCdExecutor();
            shooter?.StartExecutor(token).Forget();
        }

        /// <summary>
        ///     创建按Cd时长循环执行的执行器(一轮)(功能完成之后就销毁)
        /// </summary>
        protected ThingCdExecutor CreateCdExecutor()
        {
            try
            {
                long shootMethod = GetTotalLong(PropType.ShootMethod).FirstOrDefault();
                string clsName = $"ThingCdExecutors.ThingCdExecutor_{shootMethod}";
                Type csCls = SingletonMgr.Instance.TypeCache.GetType(clsName) ?? typeof(ThingCdExecutor);
                if (Activator.CreateInstance(csCls) is not ThingCdExecutor cdExecutor)
                {
                    throw new Exception(
                        $"未实现此执行者: ShootMethod = {shootMethod}");
                }

                cdExecutor.Thing = this;

                return cdExecutor;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }

            return null;
        }

        #endregion

        #region 击退

        /// <summary>
        ///     是否被击退
        /// </summary>
        public bool IsHitBack { get; set; }

        /// <summary>
        ///     击退速度
        /// </summary>
        public float HitBackSpeed { get; set; }

        #endregion
    }
}