﻿using Apq.ChangeBubbling;

using X.PB;

namespace Apq.Props
{
    /// <summary>
    /// 用字典来代替属性的基类(即:属性名用PropType枚举[定义在CSV.proto中])
    /// </summary>
    /// <typeparam name="T<PERSON><PERSON>"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    public class PropBase<TKey, TValue> : BubblingDic<TKey, TValue>
        where TKey : notnull
        where TValue : IBubbleNode
    {
        /// <summary>
        /// 用字典来代替属性的基类(即:属性名用PropType枚举[定义在CSV.proto中])
        /// </summary>
        /// <param name="propName">属性类型(视为名称)</param>
        /// <param name="parent">字典所属实例</param>
        public PropBase(PropType propName, IBubbleNode parent = null) : base(propName, parent)
        {
        }
    }
}