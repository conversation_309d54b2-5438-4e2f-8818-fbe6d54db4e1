﻿using System;

using Apq.ChangeBubbling;

using View;

namespace Apq.Props
{
	/// <summary>
	/// 属性值(限定为三种列表:整数、浮点数、字符串)
	/// </summary>
	public class PropValue : BubblingNode, ICloneable, IEffective
	{
		public BubblingList<long> LongValues { get; }
		public BubblingList<double> DoubleValues { get; }
		public BubblingList<string> StrValues { get; }

		/// <summary>
		/// 属性值
		/// </summary>
		/// <param name="key">属性在所属实例中的哪个键</param>
		/// <param name="parent">属性所属实例</param>
		public PropValue(object key = null, IBubbleNode parent = null)
		{
			Parent = parent;
			Key = key;

			LongValues = new(nameof(LongValues),this);
			DoubleValues = new(nameof(DoubleValues),this);
			StrValues = new(nameof(StrValues),this);
		}

		#region ICloneable
		public virtual object Clone()
		{
			var clone = new PropValue();
			CopyTo(clone);
			return clone;
		}

		public void CopyTo(PropValue other)
		{
			//// 复制时暂停更改事件
			//var changingSuspend = other.ChangingSuspend;
			//var changedSuspend = other.ChangedSuspend;
			//other.ChangingSuspend = true;
			//other.ChangedSuspend = true;

			other.LongValues.Clear();
			other.LongValues.AddRange(LongValues);
			other.DoubleValues.Clear();
			other.DoubleValues.AddRange(DoubleValues);
			other.StrValues.Clear();
			other.StrValues.AddRange(StrValues);

			//// 复制后恢复更改事件
			//other.ChangingSuspend = changingSuspend;
			//other.ChangedSuspend = changedSuspend;
		}
		#endregion

        #region IExpire

        /// <inheritdoc />
        public Func<DateTime> NowProvider { get; set; } = () => SingletonMgr.Instance.GlobalMgr.GetServerNow();
        
        /// <inheritdoc />
        public int InvalidMode { get; set; }

        /// <inheritdoc />
        public DateTime StartTime { get; set; }

        /// <inheritdoc />
        public DateTime EndTime { get; set; }

        /// <inheritdoc />
        public bool IsTooEarly() => InvalidMode == 1 && NowProvider.Invoke() < StartTime;

        /// <inheritdoc />
        public bool IsTooLate() => InvalidMode == 1 && EndTime <= NowProvider.Invoke();

        /// <inheritdoc />
        public bool IsEffective() => InvalidMode == 0
                                     || (InvalidMode == 1 && !IsTooEarly() && !IsTooLate());

        #endregion
    }
}