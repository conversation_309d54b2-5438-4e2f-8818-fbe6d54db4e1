﻿using Apq.ChangeBubbling;

using DataStructure;

using Ext;

using View;

using X.PB;

namespace CsvTables
{
	public class ActorDataCatalogCsv : CsvDicPb3Loader<ActorDataCatalogCfg, ActorDataCatalogCfg.Types.CSVRow, int>
	{
		/// <summary>
		/// 创建数据项(不含值)
		/// </summary>
		public static ActorDataItem CreateDateItem(int dataCatalog, object key = default, IBubbleNode parent = null)
		{
			if (SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<ActorDataCatalogCsv>().Dic.TryGetValue(dataCatalog, out var dcCfg))
			{
				var rtn = new ActorDataItem(key, parent)
				{
					DataCatalog = dataCatalog,
					ValueType = (byte)dcCfg.ValueType,
					InvalidMode = dcCfg.ExpirateType > 0 ? 1 : 0,
					StartTime = dcCfg.ExpirateType switch
					{
						1 => dcCfg.StartTime.ToServerTime(),
						3 => SingletonMgr.Instance.GlobalMgr.GetServerNow().Date,
						_ => SingletonMgr.Instance.GlobalMgr.GetServerNow(),
					},
					EndTime = dcCfg.ExpirateType switch
					{
						1 => dcCfg.EndTime.ToServerTime(),
						2 => SingletonMgr.Instance.GlobalMgr.GetServerNow().AddSeconds(dcCfg.ExpirateSeconds),
						3 => SingletonMgr.Instance.GlobalMgr.GetServerNow().Date.AddDays(1),
						_ => SingletonMgr.Instance.GlobalMgr.GetServerNow(),
					},
					Remark = dcCfg.Remark,
				};

				return rtn;
			}
			return null;
		}

		/// <summary>
		/// 获取数据类别的初始值(按ActorDataCatalog配置行)
		/// </summary>
		public static ActorDataItem GetInit(int dataCatalog, object key = default, IBubbleNode parent = null)
		{
			if (SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<ActorDataCatalogCsv>().Dic.TryGetValue(dataCatalog, out var dcCfg))
			{
				var rtn = CreateDateItem(dataCatalog, key, parent);
				if (rtn != null)
				{
					rtn.LongValues.AddRange(dcCfg.LongValues);
					rtn.DoubleValues.AddRange(dcCfg.DoubleValues);
					rtn.StrValues.AddRange(dcCfg.StrValues);
				}

				return rtn;
			}
			return null;
		}
	}
}