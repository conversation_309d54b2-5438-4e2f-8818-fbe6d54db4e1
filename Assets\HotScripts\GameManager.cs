﻿// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;

using Apq;
using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using CsvTables;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using HotScripts;

using JsonStructure;

using Newtonsoft.Json;

using Props;

using RxEventsM2V;

using RxEventsV2M;

using Thing;

using UniRx;

using UnityEngine;

using View;

using ViewModel;

using X.PB;

[DisallowMultipleComponent]
public class GameManager : MonoBehaviour
{
    public CameraMovement cameraMovement;

    private MonsterPool MonsterPool_m;

    /// <summary>
    ///     是否在战斗中(背包界面不算)
    /// </summary>
    public BubblingList<bool> IsFighting { get; } = new(nameof(IsFighting));

    /// <summary>
    ///     玩家数据
    /// </summary>
    public ActorThing Actor { get; set; }

    /// <summary>
    ///     关卡管理器
    /// </summary>
    public StageMgr StageMgr { get; set; }

    /// <summary>
    ///     当前回合
    /// </summary>
    public StageRound StageRound { get; set; }

    /// <summary>
    ///     地图管理器
    /// </summary>
    public MapMgr MapMgr { get; set; }

    /// <summary>
    ///     子弹池
    /// </summary>
    public BulletPool BulletPool { get; set; }

    /// <summary>
    ///     坐标系池
    /// </summary>
    public CoordinatePool CoordinatePool { get; set; }

    /// <summary>
    ///     怪物池
    /// </summary>
    public MonsterPool MonsterPool
    {
        get
        {
            if (MonsterPool_m)
            {
                return MonsterPool_m;
            }

            GameObject gameObj = new(nameof(MonsterPool));
            MonsterPool_m = gameObj.GetOrAddComponent<MonsterPool>();
            return MonsterPool_m;
        }
    }

    /// <summary>
    ///     所有存活的怪物数据
    /// </summary>
    public ReactiveCollection<MonsterThing> Monsters { get; } = new();

    // /// <summary>
    // /// 战斗平面的格子容器
    // /// </summary>
    // public GameObject GridContainer { get; set; }

    /// <summary>
    ///     获取或设置已出生的玩家角色(界面)
    /// </summary>
    public PlayerActor PlayerActor { get; set; }

    public TimeManager TimeManager { get; private set; }

    public bool IsNeedOpenFightBag { get; set; }

    public bool UIOfferTimesOpend { get; set; }

    protected CancellationTokenSource CTS_WatchOfferTimes { get; set; } = new();
    /// <summary>
    /// 等待出生的怪物数量
    /// </summary>
    private int waitingBornMonsterNum { get; set; } = 0;

    /// <summary>
    ///     死亡标识(玩家或者回合结束)
    /// </summary>
    private bool isRoundEnd { get; set; }

    public void Awake()
    {
        SingletonMgr.Instance.BattleMgr = this;

        DOTween.SetTweensCapacity(1000, 1000);
        TimeManager = gameObject.GetOrAddComponent<TimeManager>();

        StageMgr = gameObject.GetOrAddComponent<StageMgr>();
        StageRound = gameObject.GetOrAddComponent<StageRound>();

        MapMgr = gameObject.GetOrAddComponent<MapMgr>();
        MapMgr.MapsObj = transform.GetOrAddChildGameObject("Maps");

        // 子弹池根节点
        BulletPool = transform.GetOrAddChildGameObject(nameof(BulletPool)).GetOrAddComponent<BulletPool>();
        // 坐标系池根节点
        CoordinatePool = transform.GetOrAddChildGameObject(nameof(CoordinatePool)).GetOrAddComponent<CoordinatePool>();

        isRoundEnd = false;
    }

    public void Start()
    {
        // 监视怪物被移除
        Monsters.ObserveRemove().Subscribe(_ =>
        {
            // 怪被打完，且刷怪结束，则回合结束
            if (Monsters.Count == 0 && waitingBornMonsterNum == 0 && StageRound.MonsterSpawner is { IsBrushFinish: { Value: true } } &&
                StageRound.MonsterSpawner.MonsterQueue.Count == 0)
            {
                MessageBroker.Default.Publish(new RoundEnd { Passed = true });
            }
        }).AddTo(this);

        // 处理事件:出生玩家
        MessageBroker.Default.Receive<BornPlayer>().Subscribe(e =>
        {
            Task_BornPlayer(e).Forget();
        }).AddTo(this);

        // 处理事件:出生宠物
        MessageBroker.Default.Receive<BornPet>().Subscribe(_ =>
        {
            //	foreach (var goodsID in LuaToCshapeManager.Instance.PetFightEquipmentIDs)
            //	{
            //		if (goodsID <= 0) continue;

            //		var csvRow_Equip = EquipmentCsv.Instance.Dic[goodsID];

            //		// 按配置克隆宠物的预制件
            //		var csvRow_Creature = CreatureScheme.Instance.GetItem(csvRow_Equip.RightWeapon);
            //		string petModlePath = $"Assets/Temp/{csvRow_Creature.Prefab}.prefab";
            //		var petPrefab = await ResMgr.LoadResAsyncHandle<GameObject>(petModlePath).Task;
            //		var petObject = Instantiate(petPrefab, player.transform.parent);

            //		// 初始位置:距离和随机角度
            //		var distanceToMaster = 12f;
            //		var angle = RandomNum.RandomFloat(0, 360);
            //		var p = player.transform.position + distanceToMaster * Vector3.right;
            //		petObject.transform.position = p.RotateAround(player.transform.position, Vector3.forward, angle);
            //		petObject.transform.localScale = 2.25f * Vector3.one;
            //		//Debug.Log($"player位置:{player.transform.position}");
            //		//Debug.Log($"宠物初始位置:{petObject.transform.position}");

            //		var pet = petObject.AddComponent<Pet>();
            //		//pet.FightProp.InitPet(player);

            //		//// 给宠物添加技能
            //		//if (csvRow_Equip.ConsignmentStyle > 0)
            //		//{
            //		//	pet.SkillScheduler.AddSkill(csvRow_Equip.ConsignmentStyle);
            //		//}

            //		pet.CreatureThing.Creature = pet;
            //		pet.Master = player;
            //		player.CreatureThing.Pets.Add(pet);
            //		//pet.InitPropsEvents();
            //		pet.MoveCircle().Forget();
            //	}
        }).AddTo(this);

        // 处理事件:出生怪物
        MessageBroker.Default.Receive<BornMonster>().Subscribe(e =>
        {
            Task_BornMonster(e).Forget();
        }).AddTo(this);

        // 处理事件:出生子弹
        MessageBroker.Default.Receive<BornBullet>().Subscribe(e =>
        {
            Task_BornBullet(e).Forget();
        }).AddTo(this);

        // 处理事件:出生一个围击炸弹
        MessageBroker.Default.Receive<BornSiegeBomb>().Subscribe(e =>
        {
            Task_BornSiegeBomb(e).Forget();
        }).AddTo(this);

        // 处理事件:物件死亡
        MessageBroker.Default.Receive<ThingDead>().Subscribe(e =>
        {
            switch (e.Thing)
            {
                // 怪物死亡
                case MonsterThing monster:
                    {
                        // 只有不是边界自杀的怪物死亡才增加击杀计数
                        if (monster.DeathCause != MonsterDeathCause.BoundarySuicide)
                        {
                            KillCount.Value++;
                        }

                        if (monster.DeathCause == MonsterDeathCause.KilledByPlayer)
                        {
                            // 玩家增加供选经验值（基础经验）
                            Actor.AddOfferExp(monster.CsvRow_BattleBrushEnemy.Skill);

                            List<int> propIds = Actor.GetTotalLong(PropType.KilledAwardPropIdList)
                                .ConvertAll(x => (int)x);
                            List<double> propPriorities = Actor.GetTotalDouble(PropType.KilledAwardPropIdPriorityList);
                            // 记录新加入的属性
                            List<CommonProp> lstPropsAdd = new();
                            Actor.CreateProps(propIds, propPriorities).ForEach(prop =>
                            {
                                lstPropsAdd.AddRange(Actor.AddBattleProp(prop));
                            });
                            // 分发新加入的属性
                            Actor.DispatchBattleProps(lstPropsAdd);
                        }

                        Monsters.Remove(monster);

                        // 怪物还回怪物池
                        monster.Monster.TurnToPool().Forget();
                        if (isRoundEnd)
                        {
                            StageRound.MonsterSpawner?.StopBrush();
                            for (int i = Monsters.Count - 1; i >= 0; i--)
                            {
                                MonsterThing monster1 = Monsters[i];
                                Monsters.Remove(monster1);

                                // 怪物还回怪物池
                                monster1.Monster.TurnToPool().Forget();
                            }
                        }
                    }
                    break;
                // 玩家死亡
                case ActorThing actor:
                    {
                    }
                    break;
            }
        }).AddTo(this);

        // 处理事件:关闭战斗背包
        MessageBroker.Default.Receive<CloseFightBag>().Subscribe(_ =>
        {
            // 角色已出生过，通过Npc开始回合
            if (PlayerActor)
            {
                PlayerActor.DenyMove = false;
                return;
            }

            // 角色未出生过，直接开始回合
            MessageBroker.Default.Publish(new RoundStart());
        }).AddTo(this);

        // 处理事件:回合结束
        MessageBroker.Default.Receive<RoundEnd>().Subscribe(e =>
        {
            Task_RoundEnd(e).Forget();
        }).AddTo(this);

        // 处理事件:回合开始
        MessageBroker.Default.Receive<RoundStart>().Subscribe(e =>
        {
            Task_RoundStart(e).Forget();
        }).AddTo(this);

        // 处理事件:物件受击
        MessageBroker.Default.Receive<HitThingCells>().Subscribe(e =>
        {
            bool hitRole = false;
            if (e.Thing.Camp == Camp.Player)
            {
                hitRole = true;
            }
            // 飘字的位置
            Vector3 hudStartPos = e.Thing.Position;
            if (e.Inc_Hp > 0)
            {
                //掉血飘字
                HudMgr.Instance.SpwanDamageHud(hudComp =>
                {
                    hudComp.Init(hudStartPos);
                    hudComp.SetDamageNumber((int)e.Inc_Hp, e.IsCritical,hitRole);
                }).Forget();
            }

            if (e.Inc_Armor > 0)
            {
                //掉护甲飘字
                HudMgr.Instance.SpwanDamageHud(hudComp =>
                {
                    hudComp.Init(hudStartPos);
                    hudComp.SetDamageNumber((int)e.Inc_Armor, e.IsCritical,hitRole);
                }).Forget();
            }

            if (e.Thing.Camp == Camp.Player && e.Inc_Hp > 0)
            {
                Actor.TotalTakenDamage_Round += e.Inc_Hp;

                UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
                if (ui != null)
                {
                    ui.PlayerHpBarDoFade();
                }
            }
        }).AddTo(this);

        // 处理事件:物件Cd计时
        MessageBroker.Default.Receive<ThingCd>().Where(e => e.Thing is GunThing
        {
            Actor: not null,
            CsvRow_Gun:
            {
                Value: { GunType: not ActionType.CoinBag }
            }
        }).Subscribe(e =>
        {
            GunThing gun = (e.Thing as GunThing)!;

            Debug.Log($"枪[{gun.CsvRow_Gun.Value.Id}]的CD时长: {e.CdMax} 秒");

            UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (uiFight != null)
            {
                uiFight.OnGunCDReset(gun.Guid, (float)gun.GetTotalDouble(PropType.Cd).FirstOrDefault());
            }
        }).AddTo(this);

        // 处理事件:播放枪的射击声音
        MessageBroker.Default.Receive<PlayShootSound>().Subscribe(e =>
        {
            AudioPlayer.Instance.PlaySound(e.Shooter.Thing.GetTotalString(PropType.ShootSound).FirstOrDefault())
                .Forget();
        }).AddTo(this);

        // 处理事件:经验值变化
        MessageBroker.Default.Receive<ShowOfferExp>().Subscribe(e =>
        {
            UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (ui)
            {
                ui.ExpChange(e.CurrentValue, e.MaxValue);
            }
        }).AddTo(this);

        // 处理事件:银币变化
        MessageBroker.Default.Receive<ShowCoins>().Subscribe(e =>
        {
            UIFight ui = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (ui)
            {
                ui.CoinChange(e.Num);
            }
        }).AddTo(this);

        // 处理事件:枪升级了(战斗中)
        MessageBroker.Default.Receive<GunLvlUp>().Subscribe(e =>
        {
            UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
            if (uiFight != null)
            {
                uiFight.OnGunLevelUp(e.Gun.Guid, e.Gun.ThingLvl.Value,
                    e.Gun.CsvRow_Gun.Value.LvlIcons[e.Gun.ThingLvl.Value - 1]);
            }
        }).AddTo(this);

        EnterBattle().Forget();
    }

    public void OnDestroy()
    {
        SingletonMgr.Instance.ActorDataMgr.Data.ChangedBubbling -= ActorDataChangedBubbling_Fighting;
        
        StageRound.StopMonsterSpawner();
        StopBattle();

        DOTween.KillAll();

        SingletonMgr.Instance.BattleMgr = null;
    }

    /// <summary>
    ///     加载地图、出生角色后开始刷怪
    /// </summary>
    private async UniTaskVoid Task_BornPlayer(BornPlayer e)
    {
        // 角色初始位置
        Actor.Position = new Vector3(
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos[0],
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos[1],
            StageRound.Map.CsvRow_CatMainMap.Value.ActorPos.Skip(2).FirstOrDefault());

        if (PlayerActor)
        {
            Destroy(PlayerActor.gameObject);
        }

        string prefabPath =
            $"Assets/Temp/model/prefab/wxr/{e.ActorThing.GetTotalString(PropType.Model).FirstOrDefault()}.prefab";
        GameObject prefab = await ResMgrAsync.LoadResAsync<GameObject>(prefabPath);

        PlayerActor = Instantiate(prefab, transform).GetOrAddComponent<PlayerActor>();
        PlayerActor.transform.position = e.ActorThing.Position;
        PlayerActor.SyncToThing = true;
        PlayerActor.Thing = e.ActorThing;
        e.ActorThing.ThingBehaviour = PlayerActor;

        // 添加刚体和碰撞盒
        PlayerActor.gameObject.GetOrAddComponent<Rigidbody>();
        CircleCollider2D actorCollider = PlayerActor.gameObject.GetOrAddComponent<CircleCollider2D>();
        actorCollider.isTrigger = true;
        actorCollider.radius = e.ActorThing.TotalProp_Radius;

        // 添加移动组件
        PlayerActor.PlayerMove = PlayerActor.gameObject.GetOrAddComponent<PlayerMove>();
        PlayerActor.DenyMove = false;

        CancellationTokenSource cts = CancellationTokenSource.CreateLinkedTokenSource(CTS_WatchOfferTimes.Token,
            this.GetCancellationTokenOnDestroy());
        WatchOfferTimes(cts.Token).Forget();

        // 重建刷怪器并启动
        StageRound.StartMonsterSpawner();
        //设置玩家枪cd显示
        UIMgr.Instance.GetUI<UIFight>(UIType.UIFight).SetUIInfoOnRoundStart();

        // 依次延时启动枪
        _ = Actor.Guns.Where(g => g.IsHidden).Select((x, i) => x.StartCdExecutor(i * 50)).ToList();
        
        // 监听角色数据改变
        SingletonMgr.Instance.ActorDataMgr.Data.ChangedBubbling += ActorDataChangedBubbling_Fighting;
    }

    /// <summary>
    /// 战斗中处理角色数据的改变
    /// </summary>
    private void ActorDataChangedBubbling_Fighting(ChangeBubblingEventArgs e)
    {
        var first = e.BubblingList.FirstOrDefault();
        if (first is ActorDataItem data && Util.IsEquals(data.Parent, SingletonMgr.Instance.ActorDataMgr.Data))
        {
            if (data.DataCatalog == (int)ActorDataCatalog.BattleBagGuns)
            {
                // 从本地数据中读取枪并更新枪和角色(数据和界面)
                string jsonGunsInBag =
                    SingletonMgr.Instance.ActorDataMgr.GetStringByIndex((int)ActorDataCatalog.BattleBagGuns, 1);
                if (string.IsNullOrEmpty(jsonGunsInBag))
                {
                    jsonGunsInBag = "[]";
                }

                List<GunItem> gunsInBag =
                    JsonConvert.DeserializeObject<List<GunItem>>(jsonGunsInBag) ?? new List<GunItem>();
                Actor.UpdateGuns(gunsInBag, true);
            }
        }
    }

    private async UniTaskVoid Task_BornMonster(BornMonster e)
    {
        waitingBornMonsterNum++;
        // 出生预示
        if (e.MonsterThing.ReviveCount == 0)
        {
            EffectMgr.Instance.ShowEffect(EffectPath.BornCircle, e.MonsterThing.Position, 2, null,
                e.MonsterThing.CsvRow_BattleBrushEnemy.AttackRate * 0.001f).Forget();
            await UniTask.Delay(e.MonsterThing.CsvRow_BattleBrushEnemy.AttackRate);
        }

        MonsterBase monster = await MonsterPool.GetOrCreateMonsterFromPool(e.CsvRow_Brush.EnemyName, e.MonsterThing);

        // 加上碰撞盒
        CircleCollider2D monsterCollider = monster.gameObject.GetOrAddComponent<CircleCollider2D>();
        monsterCollider.radius = e.MonsterThing.TotalProp_Radius;
        // 不要碰撞盒了
        Destroy(monsterCollider);
        //添加寻路组件
        monster.MonsterMoveAI = monster.gameObject.GetOrAddComponent<MonsterMoveAI>();
        monster.MonsterMoveAI.NavAgent = monster.gameObject.GetOrAddComponent<NavAgent>();
        monster.MonsterMoveAI.NavAgent.NavData = StageRound.Map.NavData;

        // 怪物初始位置
        monster.transform.position = e.MonsterThing.Position;
        monster.SyncToThing = true;

        //Debug.Log($"新生怪物的速度:{e.MonsterThing.GetTotalDouble(PropType.Speed).FirstOrDefault()}");

        // 显示怪物
        monster.gameObject.SetActive(true);
        waitingBornMonsterNum--;
        // 新出生的怪物加到怪物列表
        Monsters.Add(e.MonsterThing);

        // 下次可以撞击敌人的时间
        e.MonsterThing.NextImpactEnemyTime.Value = Time.time;

        // 启动枪(依次延时)
        _ = e.MonsterThing.Guns.Where(g => g.IsHidden).Select((x, i) => x.StartCdExecutor(i * 50)).ToList();

        // 开始移动
        monster.StartMove();
    }

    private async UniTaskVoid Task_BornBullet(BornBullet e)
    {
        // 找到子弹配置
        BulletCfg.Types.CSVRow csvRow_Bullet = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Pb
            .CSVTable.First(x =>
                x.BulletId == e.Bullet.CdExecutor.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault() &&
                x.BulletLvl == e.Bullet.CdExecutor.Thing.ThingLvl.Value);

        // 子弹图片
        string bulletImg = csvRow_Bullet.Img;
        // 子弹缩放
        float bulletScale = (float)e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.BulletImgScale)
            .FirstOrDefault();
        bulletScale = bulletScale <= float.Epsilon ? 1 : bulletScale;

        // 注意：我们已经在MonsterGunCdExecutor的DoShoot方法中添加了播放怪物攻击动画的逻辑
        // 所以这里不再需要重复触发攻击动画
        // 这样可以确保在子弹创建前而不是创建后播放动画

        e.Bullet.Provider_BeforeInitView = view =>
        {
            if (view is MonsterMissileTrackPos monsterMissileTrackPos)
            {
                // 创建导弹发射器
                GameObject missileEjector =
                    SingletonMgr.Instance.BattleMgr.CoordinatePool.GetOrCreateCoordinate("MissileEjector");
                missileEjector.transform.position = e.Bullet.Position;
                
                // 确保MissileLocusGenerator始终被创建，以避免空引用
                monsterMissileTrackPos.MissileLocusGenerator = new MissileLocusGenerator
                {
                    MissileEjector = missileEjector
                };
                
                // 修复：在使用TrackPosition之前检查它是否有值
                if (e.Bullet.TrackPosition.HasValue)
                {
                    missileEjector.transform.right = e.Bullet.TrackPosition.Value - e.Bullet.Position;
                    // 更新已创建的MissileLocusGenerator的TargetPosition
                    monsterMissileTrackPos.MissileLocusGenerator.TargetPosition = e.Bullet.TrackPosition;
                    monsterMissileTrackPos.Locus =
                        monsterMissileTrackPos.MissileLocusGenerator.CalcKeyPositions(e.Bullet.Position);
                }
                else
                {
                    // 如果没有目标位置，设置一个默认的目标位置（例如前方10单位处）
                    Vector3 defaultTarget = e.Bullet.Position + missileEjector.transform.right * 10f;
                    monsterMissileTrackPos.MissileLocusGenerator.TargetPosition = defaultTarget;
                    monsterMissileTrackPos.Locus =
                        monsterMissileTrackPos.MissileLocusGenerator.CalcKeyPositions(e.Bullet.Position);
                    Debug.LogWarning("子弹的TrackPosition为空，使用默认目标位置");
                }
            }
            else if (view is ActorMissileTrackPos actorMissileTrackPos)
            {
                // 角色的抛物线子弹不需要特殊的发射器设置
                // 轨迹生成器将在OnAfterInitView中初始化
                Debug.Log($"创建角色抛物线子弹，目标位置: {e.Bullet.TrackPosition}");
            }
        };

        Type bulletCls = e.Bullet.GetBulletViewClass();
        BulletBase bullet = await BulletPool.GetOrCreateFromPool(bulletCls, e.Bullet);

        if (!string.IsNullOrWhiteSpace(bulletImg))
        {
            string sortingLayerName = bullet switch
            {
                ActorLocusBullet => "PlayerBullet",
                ActorMissileTrackPos => "PlayerBullet",
                _ => "EnemyBullet"
            };
            bullet.SpriteRenderer.SetSprite(bulletImg, sortingLayerName, bulletScale, 0,
                    bullet.GetCancellationTokenOnDestroy())
                .Forget();
        }

        // 子弹朝向运动方向
        Vector3 dir_1 = (bullet.BulletThing.Position - e.PositionPre).normalized;
        bullet.transform.right = dir_1;

        // 子弹初始位置
        bullet.transform.position = bullet.BulletThing.Position;
        bullet.SyncToThing = true;

        // 子弹生命开始时间、下次可以击中敌人的时间
        e.Bullet.LifeBeginTime.Value = e.Bullet.NextHitEnemyTime.Value = Time.time;

        //Debug.Log(
            //$"子弹出生时间:{Time.time} 剩余反弹次数:{e.Bullet.BounceTimes.Value} 最大生命时长:{e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod).FirstOrDefault()}");

        // // 等一帧再移动
        // await UniTask.NextFrame();

        if (bullet is ActorLocusBullet actorLocusBullet)
        {
            // 生成子弹轨迹后显示并开始自转和移动
            actorLocusBullet.GenLocusThenStartMove().Forget();
        }
        else if (bullet is MonsterLocusBullet monsterLocusBullet)
        {
            // 生成子弹轨迹后显示并开始自转和移动
            monsterLocusBullet.GenLocusThenStartMove().Forget();
        }
        else if (bullet is ActorMissileTrackPos actorMissileTrackPos)
        {
            // 角色抛物线子弹直接开始移动
            actorMissileTrackPos.StartMove();
        }
        else
        {
            // 子弹开始移动
            bullet.StartMove();
        }
    }

    private async UniTaskVoid Task_BornSiegeBomb(BornSiegeBomb e)
    {
        // 找到子弹配置
        BulletCfg.Types.CSVRow csvRow_Bullet = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Pb
            .CSVTable.First(x =>
                x.BulletId == e.Bullet.CdExecutor.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault() &&
                x.BulletLvl == e.Bullet.CdExecutor.Thing.ThingLvl.Value);

        // 子弹图片
        string bulletImg = csvRow_Bullet.Img;
        // 子弹缩放
        float bulletScale = (float)e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.BulletImgScale)
            .FirstOrDefault();
        bulletScale = bulletScale <= float.Epsilon ? 1 : bulletScale;

        e.Bullet.Provider_BeforeInitView = view =>
        {
            if (view is MonsterSiegeBomb monsterSiegeBomb)
            {
                monsterSiegeBomb.TargetPosition = e.TargetPosition;
            }
        };

        Type bulletCls = e.Bullet.GetBulletViewClass();
        BulletBase bullet = await BulletPool.GetOrCreateFromPool(bulletCls, e.Bullet);

        if (!string.IsNullOrWhiteSpace(bulletImg))
        {
            string sortingLayerName = bullet switch
            {
                ActorLocusBullet => "PlayerBullet",
                _ => "EnemyBullet"
            };
            bullet.SpriteRenderer.SetSprite(bulletImg, sortingLayerName, bulletScale, 0,
                    bullet.GetCancellationTokenOnDestroy())
                .Forget();
        }

        // 子弹初始位置
        bullet.transform.position = bullet.BulletThing.Position;
        bullet.SyncToThing = true;

        // // 子弹生命开始时间、下次可以击中敌人的时间
        // e.Bullet.LifeBeginTime.Value = e.Bullet.NextHitEnemyTime.Value = Time.time;

        // Debug.Log(
        //     $"子弹出生时间:{Time.time} 剩余反弹次数:{e.Bullet.BounceTimes.Value} 最大生命时长:{e.Bullet.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod).FirstOrDefault()}");

        // 显示子弹
        bullet.gameObject.SetActive(true);

        // 等一帧再移动
        await UniTask.NextFrame();

        // // 子弹开始自转
        // bullet.StartRotate();
        // 子弹开始移动
        bullet.StartMove();
    }

    public async UniTaskVoid Task_RoundStart(RoundStart e)
    {
        // 设置回合
        StageRound.RoundNo.Value = Actor.RoundNo.Value;
        // 切换地图
        UniTask task_SwitchMap = StageRound.SwitchMap();

        // 从本地数据中读取枪并重建枪和角色
        string jsonGunsInBag =
            SingletonMgr.Instance.ActorDataMgr.GetStringByIndex((int)ActorDataCatalog.BattleBagGuns, 1);
        if (string.IsNullOrEmpty(jsonGunsInBag))
        {
            jsonGunsInBag = "[]";
        }

        List<GunItem> gunsInBag = JsonConvert.DeserializeObject<List<GunItem>>(jsonGunsInBag) ?? new List<GunItem>();
        Actor.ReCreateGuns(gunsInBag);
        Actor.ReCalcHoistAndTotal_All();

        Actor.TotalTakenDamage_Round = 0;
        IsFighting.Value = true;
        int oldKillCount = KillCount.Value;
        KillCount.Value = 0;
      //  Debug.Log($"【回合开始】KillCount从{oldKillCount}重置为0");
        // 护甲每回合都从最大值开始
        double maxArmorPct = Actor.GetTotalDouble(PropType.MaxArmorPct).FirstOrDefault();
        double maxArmor = Actor.TotalProp_MaxHp * maxArmorPct;
        Actor.Armor.Value = maxArmor;

        {
            // 比较当前最大血量与进背包时的最大血量，当前血量随之增加(不随之减少)
            double maxHp = Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault();
            double maxHp_Store =
                SingletonMgr.Instance.ActorDataMgr.GetDoubleByIndex((int)ActorDataCatalog.BattleHp, 1);
            double currentHp = Actor.Hp.Value;
            double inc = maxHp - maxHp_Store;
            if (inc > 0)
            {
                currentHp += inc;
            }

            // 当前血量不能超过最大血量
            if (currentHp > maxHp)
            {
                currentHp = maxHp;
            }

            Actor.Hp.Value = currentHp;
        }

        Globals.SetZoomValueWhileGame(Globals.CocosToUnity(400));
        // Globals.resetControls = false;
        //Globals.focusOnPlayer = false;
        TimeManager.ResetTimescale();

        // 等到切换地图完成
        await task_SwitchMap;
        // 出生角色并开始刷怪
        MessageBroker.Default.Publish(new BornPlayer { ActorThing = Actor });
    }

    public async UniTaskVoid Task_RoundEnd(RoundEnd e)
    {
        // Debug.Log($"回合结束:{DateTime.Now:O}");
        await UniTask.SwitchToMainThread();

        IsFighting.Value = false;

        // 停止枪
        _ = Actor.Guns.Select(g =>
        {
            g.StopCdExecutor();
            return g;
        }).ToList();

        if (e.Passed)
        {
            // 给这回合的通过奖励
            Actor.Coins.Value += StageRound.RoundCfg.Coins +
                                 Actor.GetTotalLong(PropType.RoundCoin).FirstOrDefault();
            EffectMgr.Instance.ShowEffect(EffectPath.ui_GainCoin, transform.position, 1, UIMgr.Instance.UIRoot, 2)
                .Forget();
        }

        // Debug.Log($"已发放金币:{DateTime.Now:O}");

        // 保存该回合的统计
        Dictionary<string, string> ps = new()
        {
            ["ApiVersion"] = "v1",
            ["ActorId"] = Actor.ActorId.ToString(),
            ["StageType"] = Actor.StageType.ToString(),
            //["StageLvl"] = BattleProgress.ActorBattleProgress.StageLvl.ToString(),
            //["GotCoins"] = RoundStat.Instance.GotCoins.Value.ToString(),
            ["KillCount"] = Score.Value.ToString()
        };
        UnityHttpHelper.GetResponseString(
            LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/AddActorBattleStat_Day",
            HttpMethod.Post, ps).Forget();

        Dictionary<int, List<BattleBrushEnemy.Item>> lst =
            SingletonMgr.Instance.GlobalMgr.ListBrushMonster_Stage(StageMgr.CsvRow_Stage.Value);
        if (lst.ContainsKey(Actor.RoundNo.Value + 1))
        {
            // 无伤奖励属性
            if (Actor.TotalTakenDamage_Round <= double.Epsilon)
            {
                List<long> propIds = Actor.GetTotalLong(PropType.NotInjuriedPropIdList);
                // 记录新加入的属性
                List<CommonProp> lstPropsAdd = new();
                propIds.ForEach(id =>
                {
                    CommonProp prop = new CommonProp().SetRowData(SingletonMgr.Instance.CsvLoaderMgr
                        .GetOrAddLoader<CommonPropCsv>()
                        .Dic[(int)id]);
                    lstPropsAdd.AddRange(Actor.AddBattleProp(prop));
                });
                // 分发新加入的属性
                Actor.DispatchBattleProps(lstPropsAdd);
            }

            // 进入下一回合  +++++++++++++++++++++++++刷枪后再保存进度
            Actor.RoundNo.Value++;
            Actor.SaveProgress();

            // 保存当前血量和最大血量
            List<double> lstHp = new() { Actor.Hp.Value, Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault() };
            SingletonMgr.Instance.ActorDataMgr.SetDouble((int)ActorDataCatalog.BattleHp, lstHp.ToArray());

            if (UIMgr.Instance.GetGuideStep() == 8)
            {
                UIMgr.Instance.OpenUI(UIType.UIFightGuide, 110, (ui) => { ui.GetComponent<UIFightGuide>().Init(); });
            }
            // 创建Npc
            await StageRound.Map.NpcMgr.CreateNpc(Timing.AfterRoundSuccess);
            StageRound.Map.NpcMgr.NpcList.ForEach(t =>
            {
                t.Provider_ActorEnterTrigger = () =>
                {
                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.ShowUi))
                    {
                        if (t.NpcThing.CsvRow_Npc.Value.ShowUiId == 0)
                        {
                            StageRound.Map.NpcMgr.NpcList.Remove(t);

                            //if (UIMgr.Instance.GetGuideStep() == 8)
                            //{
                            //    UIMgr.Instance.OpenUI(UIType.FightBag, 110, ui =>
                            //    {
                            //        ui.GetComponent<UIFightGuide>().Init();
                            //    });
                            //}
                            //else
                            {
                                // UIFight uiFight = UIMgr.Instance.GetUI<UIFight>(UIType.UIFight);
                                //uiFight.BottomRoot.DOLocalMoveY(uiFight.BottomRoot.localPosition.y - 520, 0.5f).oncomplete = () =>
                                {
                                    // 打开背包前,保存最大血量
                                    PlayerActor.DenyMove = true;
                                    SingletonMgr.Instance.ActorDataMgr.SetDoubleByIndex((int)ActorDataCatalog.BattleHp,
                                        1, Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                                    // 打开背包界面
                                    UIMgr.Instance.OpenUI(UIType.FightBag, default, uiObj =>
                                    {
                                        UIFightBag uiFightBag = uiObj.GetComponent<UIFightBag>();
                                        if (uiFightBag)
                                        {
                                            uiFightBag.Init();
                                            uiFightBag.ResetProgress(true).Forget();
                                        }
                                    });
                                }
                            }
                        }
                    }

                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.NextRound))
                    {
                        // 销毁所有NPC
                        StageRound.Map.NpcMgr.NpcList.Where(p => p && p != t).ToList().ForEach(p =>
                        {
                            Destroy(p.gameObject);
                        });
                        StageRound.Map.NpcMgr.NpcList.Clear();

                        // 开始下一回合
                        MessageBroker.Default.Publish(new RoundStart());
                    }

                    if (t.NpcThing.CsvRow_Npc.Value.TriggerActions.Contains(TriggerAction.DestroyNpc))
                    {
                        Destroy(t.gameObject, 0.1f);
                    }
                };
            });
        }
        else
        {
            // 没有下一回合，通关了

            // 清除战斗进度
            LuaToCshapeManager.Instance.ClearProgress();
            //CTS_WatchOfferTimes.Cancel();
            //CTS_WatchOfferTimes = new();
            //int addFactor = (int)Actor.GetHoistedDouble(PropType.StagePrizePass).FirstOrDefault();
            //int battleBrushEnemyId = dic[BattleProgress.ActorBattleProgress.RoundNo].BattleBrushEnemyId;
            //LuaManager.Instance.LuaState_.Call("DataService.OpenSettleAccounts", Globals.CsvRow_CatMainStage.Id, true, battleBrushEnemyId, addFactor, true);
            SettleAccounts(true);
        }
    }

    /// <summary>
    ///     副本结算
    /// </summary>
    /// <param name="isPassed">是否通关</param>
    public void SettleAccounts(bool isPassed)
    {
        // 停止刷怪
        StageRound.MonsterSpawner?.StopBrush();
        StageRound.MonsterSpawner?.MonsterQueue.Clear();
        
        // 所有怪物停止枪并清空
        foreach (var monster in Monsters)
        {
            monster.StopCdExecutor();
            monster.Guns.ToList().ForEach(g=>g.StopCdExecutor());
        }
        Monsters.Clear();
        // 角色停止枪
        Actor.Guns.ToList().ForEach(g=>g.StopCdExecutor());
        
        // 结算
        double addFactor = Actor.GetTotalDouble(PropType.StagePrizePass).FirstOrDefault();
        //var battleBrushEnemyId = dic[Actor.RoundNo].BattleBrushEnemyId;
        LuaManager.Instance.LuaState_.Call("DataService.OpenSettleAccounts",
            StageMgr.StageLvl, isPassed,
            Actor.RoundNo.Value, addFactor, true);
    }

    public void BattleQuit()
    {
        // 停止刷怪
        StageRound.MonsterSpawner?.StopBrush();
        StageRound.MonsterSpawner?.MonsterQueue.Clear();

        // 所有怪物停止枪并清空
        foreach (var monster in Monsters)
        {
            monster.StopCdExecutor();
            monster.Guns.ToList().ForEach(g => g.StopCdExecutor());
        }
        Monsters.Clear();
        // 角色停止枪
        Actor.Guns.ToList().ForEach(g => g.StopCdExecutor());
    }

    /// <summary>
    ///     战斗入口(读取角色属性、武器列表、进度恢复、关卡配置、加载地图、打开背包界面)
    /// </summary>
    public async UniTaskVoid EnterBattle()
    {
        // 初始化角色数据
        Actor = new ActorThing
        {
            ActorId = LuaDataSrvClient.Instance.GetActorID(), ActorProp = LuaDataSrvClient.Instance.GetPlayerProp()
        };
        // 角色出战的武器
        Actor.InitWeapons(LuaDataSrvClient.Instance.GetWeapons(true));

        #region 进度恢复

        // 等到通用属性表加载完成
        await UniTask.WaitUntil(() => SingletonMgr.Instance.GlobalMgr.CommonPropCfgLoaded.Value);

        Actor.ReadProgress();

        #endregion

        // 设置关卡
        StageMgr.SetStage(Actor.StageLvl, Actor.StageType);
        // 提前设置回合(切换地图)
        StageRound.RoundNo.Value = Actor.RoundNo.Value;
        StageRound.SwitchMap().Forget();

        UIMgr.Instance.OpenUI(UIType.UIFight, default, uiObj =>
        {
            UIFight uiFight = uiObj.GetComponent<UIFight>();
            if (uiFight)
            {
                uiFight.Init();
                uiFight.SetUIInfo();
            }

            MessageBroker.Default.Publish(new CloseFightBag());
            // 打开背包前,保存最大血量
            //SingletonMgr.Instance.ActorDataMgr.SetDoubleByIndex((int)ActorDataCatalog.BattleHp, 1,
            //    Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
            ////打开背包界面
            //UIMgr.Instance.OpenUI(UIType.FightBag, default, uiBag =>
            //{
            //    var uiFightBag = uiBag.GetComponent<UIFightBag>();
            //    if (uiFightBag)
            //    {
            //        uiFightBag.Init();
            //        uiFightBag.ResetProgress(Actor.RefreshTimes == 0).Forget();
            //    }
            //});
        });
    }

    public async UniTaskVoid WatchOfferTimes(CancellationToken token)
    {
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }

            if (Actor.OfferTimes.Value > 0 && !UIOfferTimesOpend)
            {
                UIOfferTimesOpend = true;
                UIUpgradeProperty ui = UIMgr.Instance.GetUI<UIUpgradeProperty>(UIType.UIUpgradeProperty);
                if (ui != null && ui.gameObject.activeSelf)
                {
                    return;
                }

                //Actor.OfferTimes.Value--;
                UIMgr.Instance.OpenUI(UIType.UIUpgradeProperty, 101);
            }
        }
    }

    /// <summary>
    ///     在距离区间内查找怪物
    /// </summary>
    /// <param name="pos">圆心</param>
    /// <param name="radius">半径</param>
    /// <param name="howFind">查找方法</param>
    /// <param name="maxDistance">最大距离</param>
    /// <param name="maxCount">最多找多少个</param>
    /// <param name="minDistance">最小距离</param>
    /// <param name="predicate">怪物范围条件</param>
    public List<DistanceThing> FindMonster(Vector3 pos, float radius, FindActionTarget howFind,
        float maxDistance, int maxCount = 1, float minDistance = 0,
        Func<MonsterThing, bool> predicate = null)
    {
        List<DistanceThing> lstDistanceMonster = Monsters.Where(x => predicate?.Invoke(x) ?? true)
            .Select(x => new DistanceThing
            {
                Thing2 = x,
                Distance = pos.CalcDistance2D_SolidCircleToSolidCircle(radius, x.Position, x.TotalProp_Radius)
            })
            .Where(x => minDistance <= x.Distance && x.Distance <= maxDistance).ToList();

        return howFind switch
        {
            FindActionTarget.NearestEnemy => lstDistanceMonster
                .OrderBy(x => x.Distance)
                .Take(maxCount)
                .ToList(),
            FindActionTarget.RandomEnemy => lstDistanceMonster
                .OrderBy(_ => RandomNum.RandomInt())
                .Take(maxCount)
                .ToList(),
            _ => null
        };
    }

    /// <summary>
    ///     结束战斗，退出战斗场景
    /// </summary>
    public void StopBattle()
    {
        // 玩家的枪停止射击
        Actor.Guns.ToList().ForEach(g => g.CTS_CdExecutor.Cancel());
        Monsters.ToList().ForEach(_ => _.CTS_CdExecutor.Cancel());
    }

    #region 回合统计

    /// <summary>
    ///     回合内增加的金币数
    /// </summary>
    public IntReactiveProperty GotCoins { get; } = new();

    /// <summary>
    ///     回合内增加的杀怪数
    /// </summary>
    public IntReactiveProperty KillCount { get; } = new();

    /// <summary>
    ///     回合内增加的积分数
    /// </summary>
    public IntReactiveProperty Score { get; } = new();

    #endregion
}