﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的连射
    /// </summary>
    public class ThingCdExecutor_1 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                return;
            }

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向
                // Vector3 shootDir = (distanceEnemy.Thing2.Position - Actor.Position).normalized;
                Vector3 shootDir = Vector3.up;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstburstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    BurstOne(cts_Skill.Token, (float)x, distanceEnemy.Thing2, null, shootDir, bulletQty,
                            burstburstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });
                //Debug.Log($"一轮发射子弹数:{bulletQty}");

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, trackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    bullet.Position = Actor.Position; // + 0.1f * dir_1;
                    bullet.MoveDirection_Straight.Value = dir_1;

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }
    }
}