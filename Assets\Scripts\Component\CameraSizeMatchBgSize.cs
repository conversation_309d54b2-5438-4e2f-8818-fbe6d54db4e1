using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// ����ӿ�����ȫ������
/// </summary>
public class CameraSizeMatchBgSize : MonoBehaviour
{
    public void Start()
    {
        MatchSize();
    }

    /// <summary>
    /// ע�⣺Ҫȷ������ͼ��������λ����Vector3(0,0,10)
    /// </summary>
    private void MatchSize()
    {
        SpriteRenderer sp = GetComponent<SpriteRenderer>();
        float widthScale = (float)Screen.width / sp.sprite.texture.width;
        float heightScale = (float)Screen.height / sp.sprite.texture.height;
        Camera.main.orthographicSize = 42f;
        //sp.sprite.texture.height * sp.transform.localScale.y * 0.1f * 0.01f;
        //float scale = Camera.main.orthographicSize * 100 * 2 / sp.sprite.texture.height;
        //sp.transform.localScale = new Vector3(scale, scale, 1);
    }
}
