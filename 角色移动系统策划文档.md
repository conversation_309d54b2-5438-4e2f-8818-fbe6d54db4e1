# 角色移动系统策划文档

## 一、系统概述

### 1.1 系统简介
角色移动系统是战斗系统的核心组成部分，负责处理玩家角色在战斗场景中的移动控制、位置计算、边界检测、碰撞处理等功能。系统采用摇杆控制方式，支持Y轴单向移动模式。

### 1.2 核心特性
- **Y轴单向移动**：角色只能沿Y轴（垂直方向）移动，X轴位置固定在出生点
- **摇杆控制**：通过虚拟摇杆进行移动操作
- **智能边界检测**：自动检测屏幕边界和地形碰撞
- **平滑移动**：支持减速、精确定位等平滑移动效果
- **动态速度计算**：基于角色属性和血量梯度的动态速度系统

## 二、核心组件架构

### 2.1 主要组件
1. **JoystickComp** - 摇杆控制组件
2. **PlayerMove** - 角色移动逻辑组件  
3. **PlayerActor** - 角色控制器
4. **DragMoveUIComp** - UI拖拽辅助组件

### 2.2 组件关系图
```
JoystickComp (摇杆控制)
    ↓ 输入处理
PlayerMove (移动逻辑)
    ↓ 动画控制
PlayerActor (角色控制器)
    ↓ 渲染更新
ThingBehaviour (基础行为)
```

## 三、摇杆控制系统

### 3.1 摇杆组件结构
- **DiPan**: 摇杆底盘，负责接收触摸输入
- **FocusPoint**: 摇杆焦点，显示摇杆中心位置
- **DragMoveUIComp**: 拖拽处理组件

### 3.2 坐标系统
#### 关键坐标点定义：
- **K点**: 角色初始位置（拖拽开始时的角色位置）
- **A点**: 手指初始位置（触摸开始的世界坐标）
- **B点**: 手指当前位置（触摸移动的实时世界坐标）
- **M点**: 角色目标位置（计算得出的角色应到达位置）
- **spawnX**: 角色出生点X坐标（固定不变）

#### 坐标计算公式：
```
M.y = K.y + (B.y - A.y)  // Y轴可变
M.x = spawnX             // X轴固定
```

### 3.3 摇杆操作流程
1. **开始拖拽（OnDragStart）**
   - 记录角色当前位置（K点）
   - 获取角色出生点X坐标（spawnX）
   - 记录手指初始位置（A点）
   - 移动摇杆UI到触摸位置
   - 启动角色移动状态

2. **拖拽中（OnDrag）**
   - 更新手指当前位置（B点）
   - 计算目标位置（M点）
   - 计算移动方向向量（仅Y轴分量）
   - 设置角色目标位置
   - 更新摇杆UI显示

3. **结束拖拽（OnDragEnd）**
   - 恢复摇杆UI位置
   - 设置最终目标位置
   - 停止角色移动状态

## 四、角色移动逻辑

### 4.1 移动状态管理
- **isMoving**: 是否正在移动
- **DenyMove**: 是否拒绝移动（用于特殊状态控制）
- **MoveDuration**: 已移动时长
- **StandDuration**: 已站立时长

### 4.2 目标位置系统
#### 关键参数：
- **MIN_MOVE_THRESHOLD**: 最小移动距离阈值（0.3f）
- **ARRIVAL_THRESHOLD**: 到达判断阈值（0.4f）
- **EARLY_STOP_THRESHOLD**: 提前停止距离阈值（0.5f）
- **SLOWDOWN_DISTANCE**: 开始减速距离阈值（0.8f）

#### 移动状态机：
1. **正常移动**: speedFactor = 1.0f
2. **减速移动**: speedFactor = 0.2f（SLOWDOWN_FACTOR）
3. **精确定位**: 最大持续时间0.5秒
4. **强制停止**: 超时或到达目标

### 4.3 边界检测系统
#### 屏幕边界定义：
- **左边界**: viewportPoint.x ≤ 0.05f
- **右边界**: viewportPoint.x ≥ 0.95f  
- **下边界**: viewportPoint.y ≤ 0.02f
- **上边界**: viewportPoint.y ≥ 0.87f

#### 边界处理逻辑：
1. 检测下一步移动是否超出边界
2. 限制对应轴向的移动分量
3. 边界卡住时自动向内调整位置

### 4.4 碰撞检测系统
- **射线检测距离**: 2.2f（MoveRayDis）
- **检测层级**: "Terrain"层
- **碰撞处理**: 计算贴墙移动方向
- **反射算法**: 基于法线的方向反射计算

## 五、速度计算系统

### 5.1 基础速度获取
```csharp
var speed = SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.Speed).FirstOrDefault()
```

### 5.2 血量梯度影响
#### 计算公式：
```csharp
// 血量比例
var hpPct = Actor.Hp.Value / Actor.TotalProp_MaxHp;

// 梯度索引
var idx = Actor.GetTotalDouble(PropType.HpPctGradientList)
    .Where(x => x > hpPct).Select((_, i) => i).LastOrDefault();

// 最终速度
var finalSpeed = baseSpeed * (1 + speedPctGradient[idx]);
```

### 5.3 移动时长影响
移动时长会影响攻击力、暴击率等属性，间接影响战斗表现：
- **MoveDurationGradientList**: 移动时长梯度列表
- **AttackPctGradientListByMoveDuration**: 基于移动时长的攻击力加成
- **CriticalHitRatePctGradientListByMoveDuration**: 基于移动时长的暴击率加成

## 六、动画系统

### 6.1 动画状态
- **idle01**: 待机动画
- **move01**: 移动动画  
- **attack01**: 攻击动画（优先级最高）

### 6.2 朝向控制
#### 朝向规则：
- **direction = 1**: 朝左
- **direction = -1**: 朝右
- **判断条件**: allowedDir.x >= 0 ? -1 : 1

#### 防抖动机制：
- **DIRECTION_STABILIZE_DELAY**: 方向稳定延迟（0.1秒）
- **DIRECTION_FLIP_INTERVAL**: 翻转间隔限制（0.2秒）
- **DIRECTION_CHANGE_THRESHOLD**: 方向改变角度阈值（30度）

## 七、UI系统集成

### 7.1 坐标转换
- **屏幕坐标转世界坐标**: Camera.main.ScreenToWorldPoint()
- **屏幕坐标转UI坐标**: UIMgr.Instance.GetUICamera().ScreenToWorldPoint()
- **UI局部坐标转换**: RectTransformUtility.ScreenPointToLocalPointInRectangle()

### 7.2 摇杆UI更新
- **底盘跟随**: 摇杆底盘跟随触摸位置移动
- **焦点居中**: 摇杆焦点始终保持在底盘中心
- **位置重置**: 拖拽结束后恢复到初始位置

## 八、调试与日志系统

### 8.1 日志分类
- **【摇杆操作】**: 摇杆相关操作日志
- **【移动日志】**: 移动状态和位置日志  
- **77777**: PlayerMove位置同步检查日志

### 8.2 关键调试信息
- K点、A点、B点、M点坐标
- 移动方向角度和距离
- 出生点坐标和当前坐标
- 边界检测和碰撞信息
- 速度计算和属性影响

## 九、配置参数表

### 9.1 移动参数
| 参数名 | 数值 | 说明 |
|--------|------|------|
| MoveRayDis | 2.2f | 射线检测距离 |
| MIN_MOVE_THRESHOLD | 0.3f | 最小移动阈值 |
| ARRIVAL_THRESHOLD | 0.4f | 到达判断阈值 |
| EARLY_STOP_THRESHOLD | 0.5f | 提前停止阈值 |
| SLOWDOWN_DISTANCE | 0.8f | 减速距离阈值 |
| SLOWDOWN_FACTOR | 0.2f | 减速因子 |
| MAX_FINAL_POSITIONING_TIME | 0.5f | 最大精确定位时间 |

### 9.2 边界参数
| 边界 | 数值 | 说明 |
|------|------|------|
| 左边界 | 0.05f | 视口坐标 |
| 右边界 | 0.95f | 视口坐标 |
| 下边界 | 0.02f | 视口坐标 |
| 上边界 | 0.87f | 视口坐标 |

### 9.3 朝向参数
| 参数名 | 数值 | 说明 |
|--------|------|------|
| DIRECTION_STABILIZE_DELAY | 0.1f | 方向稳定延迟 |
| DIRECTION_FLIP_INTERVAL | 0.2f | 翻转间隔限制 |
| DIRECTION_CHANGE_THRESHOLD | 30f | 方向改变角度阈值 |

## 十、属性系统关联

### 10.1 核心属性
- **PropType.Speed**: 基础移动速度
- **PropType.HpPctGradientList**: 血量百分比梯度列表
- **PropType.SpeedPctGradientList**: 速度百分比加成梯度列表
- **PropType.MoveDurationGradientList**: 移动时长梯度列表

### 10.2 间接影响属性
- **PropType.AttackPctGradientListByMoveDuration**: 移动时长影响攻击力
- **PropType.CriticalHitRatePctGradientListByMoveDuration**: 移动时长影响暴击率
- **PropType.CdPctGradientListByMoveDuration**: 移动时长影响冷却时间

## 十一、特殊状态处理

### 11.1 状态优先级
1. **DenyMove**: 完全禁止移动
2. **IsHitBack**: 击退状态（反向移动）
3. **IsFreeze**: 冰冻状态（速度为0）
4. **攻击动画**: 不切换移动动画

### 11.2 异常处理
- **组件缺失**: 安全检查和空值处理
- **边界卡住**: 自动向内调整
- **长时间无法到达**: 超时强制停止
- **坐标同步**: Transform与ActorThing位置同步检查

## 十二、性能优化

### 12.1 更新频率控制
- **日志输出**: 每2秒打印一次
- **位置检查**: 每秒检查一次（60帧间隔）
- **方向更新**: 仅在角度变化超过阈值时更新

### 12.2 计算优化
- **向量计算**: 使用sqrMagnitude避免开方运算
- **距离检查**: 使用阈值避免频繁更新
- **射线检测**: 仅在移动时进行检测

## 十三、实现细节代码示例

### 13.1 摇杆控制核心代码
```csharp
// Y轴移动计算
private void OnDrag(Vector2 localPos)
{
    // 更新手指当前位置（B点）
    pointB = Camera.main.ScreenToWorldPoint(Input.mousePosition);

    // 计算Y轴方向的移动，X轴保持为出生点X坐标
    float targetY = pointK.y + (pointB.y - pointA.y);
    pointM = new Vector2(spawnX, targetY);

    // 计算移动方向（只在Y轴方向）
    Vector2 currentPos = playerMoveComp.transform.position;
    Vector2 directionVector = new Vector2(0, pointM.y - currentPos.y);
    moveDir = directionVector.normalized;
    moveDis = Mathf.Abs(pointM.y - currentPos.y);

    // 设置目标位置
    playerMoveComp.SetTargetPosition(pointM);
}
```

### 13.2 速度计算核心代码
```csharp
// 血量梯度速度计算
var hpPct = SingletonMgr.Instance.BattleMgr.Actor.Hp.Value /
           SingletonMgr.Instance.BattleMgr.Actor.TotalProp_MaxHp;
var idx = SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.HpPctGradientList)
    .Where(x => x > hpPct).Select((_, i) => i).LastOrDefault();

var speed = (float)SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.Speed)
    .FirstOrDefault() * (1 + (float)SingletonMgr.Instance.BattleMgr.Actor
    .GetTotalDouble(PropType.SpeedPctGradientList).IndexOfOrFirstOrDefault(idx));
```

### 13.3 边界检测核心代码
```csharp
// 屏幕边界检测
Vector3 viewportPoint = Camera.main.WorldToViewportPoint(nextPosition);
bool hitBoundary = false;

if (viewportPoint.x <= 0.05f || viewportPoint.x >= 0.95f)
{
    moveDir.x = 0; // 限制X轴移动
    hitBoundary = true;
}

if (viewportPoint.y <= 0.02f || viewportPoint.y >= 0.87f)
{
    moveDir.y = 0; // 限制Y轴移动
    hitBoundary = true;
}
```

### 13.4 碰撞检测核心代码
```csharp
// 地形碰撞检测
RaycastHit2D hit2D = Physics2D.Raycast(transform.position, moveDir,
    MoveRayDis, 1 << LayerMask.NameToLayer("Terrain"));

if (hit2D.collider != null)
{
    // 计算贴墙移动方向
    moveDir = GetNewMoveDirOnBlock(moveDir, hit2D);

    // 二次检测
    hit2D = Physics2D.Raycast(transform.position, moveDir,
        MoveRayDis, 1 << LayerMask.NameToLayer("Terrain"));
    if (hit2D.collider != null) return; // 完全阻挡
}
```

## 十四、配置文件关联

### 14.1 怪物移动配置
- **BattleBrushEnemy.csv**: 怪物移动类型和参数配置
- **MoveType**: 移动类型（10=四方向移动，12=单向下移动）
- **MoveParams**: 移动参数数组（方向;速度;停止时间;恢复速度）

### 14.2 属性配置文件
- **PropTypeCsv**: 属性类型定义
- **GenreConfig.csv**: 装备类型属性加成配置
- **SkillProp.lua**: 技能属性映射配置

### 14.3 UI配置
- **UIRoot/Canvas/Root_UI**: UI根节点
- **UICamera**: UI专用相机
- **摇杆预制体**: DiPan + FocusPoint 结构

## 十五、扩展性设计

### 15.1 移动模式扩展
当前系统支持Y轴单向移动，可扩展为：
- **X轴单向移动**: 修改pointM计算公式
- **自由移动**: 同时计算X、Y轴偏移
- **限制区域移动**: 添加区域边界检测

### 15.2 控制方式扩展
- **键盘控制**: 添加WASD键位支持
- **手柄控制**: 集成手柄输入系统
- **AI控制**: 怪物自动移动逻辑

### 15.3 特效扩展
- **移动轨迹**: 添加移动轨迹特效
- **速度线**: 高速移动时的视觉效果
- **位移技能**: 瞬移、冲刺等特殊移动

## 十六、测试验证

### 16.1 功能测试点
1. **基础移动**: Y轴移动，X轴固定
2. **边界测试**: 四个边界的限制效果
3. **碰撞测试**: 地形阻挡和贴墙移动
4. **速度测试**: 不同血量下的速度变化
5. **UI测试**: 摇杆显示和交互响应

### 16.2 性能测试点
1. **帧率稳定性**: 移动过程中的FPS表现
2. **内存占用**: 长时间移动的内存变化
3. **CPU占用**: 射线检测和计算开销
4. **网络同步**: 多人模式下的位置同步

### 16.3 兼容性测试
1. **不同分辨率**: 各种屏幕尺寸适配
2. **不同平台**: Android、iOS、PC平台
3. **不同设备**: 高中低端设备性能
4. **网络环境**: 不同网络延迟下的表现

## 十七、维护指南

### 17.1 常见问题排查
1. **角色不移动**: 检查DenyMove状态和组件引用
2. **移动卡顿**: 检查射线检测频率和边界计算
3. **位置不同步**: 检查Transform与ActorThing同步
4. **摇杆失效**: 检查UI相机和坐标转换

### 17.2 参数调优建议
1. **移动手感**: 调整SLOWDOWN_FACTOR和减速距离
2. **响应性**: 调整MIN_MOVE_THRESHOLD和到达阈值
3. **稳定性**: 调整方向稳定延迟和翻转间隔
4. **性能**: 调整射线检测距离和更新频率

### 17.3 版本升级注意事项
1. **向后兼容**: 保持现有API接口不变
2. **配置迁移**: 新增参数提供默认值
3. **性能影响**: 评估新功能对现有性能的影响
4. **测试覆盖**: 确保所有移动场景的测试通过
