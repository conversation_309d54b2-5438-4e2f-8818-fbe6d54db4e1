// ReSharper disable InconsistentNaming

using System.Collections.Generic;

using Thing;

using UnityEngine;

namespace Apq.Unity3D.UnityHelpers
{
    /// <summary>
    /// 标准抛物线轨迹生成器
    /// </summary>
    /// <remarks>
    /// 用于生成Y轴匀速运动，X轴按抛物线变化的轨迹
    /// 符合需求：Y轴匀速运动变化，X轴按标准抛物线变化
    /// </remarks>
    public class ParabolicLocusGenerator
    {
        /// <summary>
        /// 子弹发射器
        /// </summary>
        public GameObject BulletEjector { get; set; }

        #region 抛物线参数

        /// <summary>
        /// 起始位置
        /// </summary>
        public Vector3 StartPosition { get; set; }

        /// <summary>
        /// 目标位置（终点）
        /// </summary>
        public Vector3? TargetPosition { get; set; }

        /// <summary>
        /// 抛物线顶点高度偏移（相对于起点和终点连线的中点）
        /// </summary>
        public float ApexHeight { get; set; } = 36f;

        /// <summary>
        /// X轴抛物线弧度系数（控制X轴偏移幅度）
        /// </summary>
        public float ParabolicWidth { get; set; } = 38f;

        /// <summary>
        /// 路径分段数量
        /// </summary>
        public int SegmentCount { get; set; } = 50;

        #endregion

        /// <summary>
        /// 计算抛物线路径
        /// </summary>
        /// <param name="startPos">起始位置</param>
        /// <returns>抛物线路径点列表</returns>
        public List<Vector3> CalcParabolicPath(Vector3 startPos)
        {
            StartPosition = startPos;
            
            //Debug.Log($"88888 抛物线路径计算 - StartPos:{startPos} TargetPos:{TargetPosition} ApexHeight:{ApexHeight} SegmentCount:{SegmentCount}");
            
            List<Vector3> pathPoints = new List<Vector3>();

            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线路径计算失败 - TargetPosition为空");
                return pathPoints;
            }
            
            Vector3 start = StartPosition;
            Vector3 end = TargetPosition.Value;
            
            // Y轴总距离
            float totalYDistance = end.y - start.y;
            
            // 计算抛物线顶点高度：起点和终点最高者再加上ApexHeight
            float maxY = Mathf.Max(start.y, end.y) + ApexHeight;

            // 计算关键点的X值用于日志输出
            Vector3 startPoint = CalculateStandardParabolicPoint(start, end, maxY, 0f);    // t=0 起始点
            Vector3 apexPoint = CalculateStandardParabolicPoint(start, end, maxY, 0.5f);   // t=0.5 最高点
            Vector3 endPoint = CalculateStandardParabolicPoint(start, end, maxY, 1f);      // t=1 终点
            
                ///    Debug.Log($"99999 抛物线关键点X值 - 起始点X:{startPoint.x:F2} 最高点X:{apexPoint.x:F2} 终点X:{endPoint.x:F2} StartY:{start.y:F2} EndY:{end.y:F2} MaxY:{maxY:F2}");

            for (int i = 0; i <= SegmentCount; i++)
            {
                float t = (float)i / SegmentCount;
                Vector3 point = CalculateStandardParabolicPoint(start, end, maxY, t);
                pathPoints.Add(point);
            }

            //Debug.Log($"88888 抛物线路径计算完成 - 生成{pathPoints.Count}个路径点 MaxY:{maxY}");
            return pathPoints;
        }

        /// <summary>
        /// 计算标准抛物线上的点
        /// </summary>
        /// <param name="start">起点</param>
        /// <param name="end">终点</param>
        /// <param name="maxHeight">抛物线最高点</param>
        /// <param name="t">Y轴进度（0-1）</param>
        /// <returns>抛物线上的点</returns>
        private Vector3 CalculateStandardParabolicPoint(Vector3 start, Vector3 end, float maxHeight, float t)
        {
            // Y轴匀速运动：从起点到终点按t线性插值
            float currentY = Mathf.Lerp(start.y, end.y, t);
            
            // X轴按增强的抛物线变化
            // 使用更明显的抛物线公式，增加X轴偏移效果
            
            // 计算抛物线顶点坐标
            float vertexY = maxHeight;
            float vertexX = (start.x + end.x) * 0.5f; // X轴中点
            
            // 增强X轴抛物线效果：在Y轴中点附近X轴有最大偏移
            float yProgress = t; // Y轴进度
            
            // 使用正弦函数增强X轴抛物线效果，让弧度更明显
            float xOffset = Mathf.Sin(yProgress * Mathf.PI) * ParabolicWidth;
            
            // 基础X坐标（起点到终点的线性插值）
            float baseX = Mathf.Lerp(start.x, end.x, t);
            
            // 应用X轴偏移，形成明显的抛物线弧度（Y轴镜像：X偏移取负值）
            float currentX = baseX - xOffset;
            
            // 确保Z坐标保持不变
            float currentZ = start.z;
            
                ///    //Debug.Log($"88888 抛物线点计算 - t:{t:F3} Y:{currentY:F2} BaseX:{baseX:F2} XOffset:{-xOffset:F2} FinalX:{currentX:F2}");
            
            return new Vector3(currentX, currentY, currentZ);
        }

        /// <summary>
        /// 根据进度获取抛物线上的位置
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <returns>当前位置</returns>
        public Vector3 GetPositionAtProgress(float progress)
        {
            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线位置计算失败 - TargetPosition为空");
                return StartPosition;
            }

            progress = Mathf.Clamp01(progress);
            
            Vector3 start = StartPosition;
            Vector3 end = TargetPosition.Value;
            float maxHeight = Mathf.Max(start.y, end.y) + ApexHeight;

            Vector3 position = CalculateStandardParabolicPoint(start, end, maxHeight, progress);
            
            return position;
        }

        /// <summary>
        /// 根据进度获取抛物线的切线方向
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <returns>切线方向</returns>
        public Vector3 GetTangentAtProgress(float progress)
        {
            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线切线计算失败 - TargetPosition为空");
                return Vector3.up;
            }

            progress = Mathf.Clamp01(progress);
            
            // 计算当前点和前后微小偏移点，用于求导数
            float delta = 0.01f;
            float progressBefore = Mathf.Max(0f, progress - delta);
            float progressAfter = Mathf.Min(1f, progress + delta);
            
            Vector3 posBefore = GetPositionAtProgress(progressBefore);
            Vector3 posAfter = GetPositionAtProgress(progressAfter);
            
            Vector3 tangent = (posAfter - posBefore).normalized;
            
            return tangent;
        }
    }
} 