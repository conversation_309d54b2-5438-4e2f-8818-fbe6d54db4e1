using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using View;

/// <summary>
/// 摇杆组件
/// </summary>
public class JoystickComp : MonoBehaviour
{
    /// <summary>
    /// 滑动方向
    /// </summary>
    public Vector2 MoveDirection => moveDir;
    /// <summary>
    /// 滑动距离大小
    /// </summary>
    public float MoveDis => moveDis;
    private DragMoveUIComp dragComp;
    
    private Vector2 moveDir { get; set; }
    private float moveDis { get; set; }
    /// <summary>
    /// 底盘半径
    /// </summary>
    private float radius { get; set; }
    /// <summary>
    /// 焦点
    /// </summary>
    private Transform focusPoint;

    private Vector3 initPos;

    // 记录的点
    private Vector2 pointK; // 角色初始位置
    private Vector2 pointA; // 手指初始位置
    private Vector2 pointB; // 手指当前位置
    private Vector2 pointM; // 角色目标位置
    private float logTimer = 0f; // 日志计时器

    /// <summary>
    /// 角色移动组件
    /// </summary>
    private PlayerMove playerMoveComp { get; set; }
    
    /// <summary>
    /// 角色出生点X坐标（战斗开始时的X坐标，保持不变）
    /// </summary>
    private float spawnX { get; set; }

    private void Start()
    {
        focusPoint = transform.Find("FocusPoint");
        dragComp = transform.Find("DiPan").gameObject.AddComponent<DragMoveUIComp>();
        dragComp.IsUpdateSelfPos = false; // 修改为false，摇杆底盘位置将由我们控制
        dragComp.OnDragStart = OnDragStart;
        dragComp.OnDragEnd = OnDragEnd;
        dragComp.OnDrag = OnDrag;
        radius = transform.Find("DiPan").GetComponent<RectTransform>().sizeDelta.x * 0.5f;
        initPos = transform.localPosition;
    }

    private void Update()
    {
        // 每2秒打印一次日志
        logTimer += Time.deltaTime;
        if (logTimer >= 2f)
        {
            logTimer = 0f;
            PrintPositionLog();
        }
    }

    private void PrintPositionLog()
    {
        if (playerMoveComp == null) return;

        Vector2 characterPos = playerMoveComp.transform.position;
        float distance = Vector2.Distance(pointK, pointA);
        float angle = Vector2.SignedAngle(Vector2.right, pointA - pointK);

        Debug.Log($"【移动日志】K点坐标: {pointK}, A点坐标: {pointA}, 距离: {distance}, 角度: {angle}°");
        Debug.Log($"【移动日志】角色当前坐标: {characterPos}, 移动方向角度: {Vector2.SignedAngle(Vector2.right, moveDir)}°, 目标M点: {pointM}");
        Debug.Log($"【移动日志】出生点X坐标: {spawnX}, 当前X坐标: {characterPos.x}");

        distance = Vector2.Distance(pointM, pointB);
        angle = Vector2.SignedAngle(Vector2.right, pointB - pointM);
        Debug.Log($"【移动日志】M点坐标: {pointM}, B点坐标: {pointB}, 距离: {distance}, 角度: {angle}°");
    }

    private void OnDragStart(Vector2 localPos)
    {
        // 记录角色初始位置（K点）
        if (playerMoveComp == null) playerMoveComp = SingletonMgr.Instance.BattleMgr.PlayerActor.GetComponent<PlayerMove>();
        if (playerMoveComp == null) return;

        pointK = playerMoveComp.transform.position;

        // 记录角色出生点X坐标（从战斗管理器的Actor数据中获取）
        if (SingletonMgr.Instance.BattleMgr.Actor != null)
        {
            spawnX = SingletonMgr.Instance.BattleMgr.Actor.Position.x;
        }
        else
        {
            spawnX = pointK.x; // 备用方案：使用当前X坐标
        }
        
        // 记录手指初始位置（A点）
        pointA = Camera.main.ScreenToWorldPoint(Input.mousePosition);
        pointB = pointA; // 初始时B点等于A点

        // 移动摇杆到触摸位置
        Vector2 clickPos = UIMgr.Instance.GetUICamera().ScreenToWorldPoint(Input.mousePosition);
        transform.position = clickPos;
        transform.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y, 0);

        // 初始时M点等于K点，但强制X坐标为出生点X坐标
        pointM = new Vector2(spawnX, pointK.y);

        playerMoveComp.StartMove();
        // 设置目标位置为当前位置（初始不移动）
        playerMoveComp.SetTargetPosition(pointM);

        Debug.Log($"【摇杆操作】开始拖拽 - 出生点X: {spawnX}, 当前位置: {pointK}, 目标位置: {pointM}");
    }

    private void OnDragEnd(Vector2 localPos)
    {
        // 恢复摇杆位置
        transform.localPosition = initPos;
        // 重置摇杆焦点位置
        focusPoint.localPosition = Vector3.zero;

        if (playerMoveComp == null) return;
        
        // 停止移动前，设置最终目标位置为最后的M点（确保X坐标为出生点X坐标）
        pointM = new Vector2(spawnX, pointM.y);
        playerMoveComp.SetTargetPosition(pointM);
        playerMoveComp.StopMove();

        Debug.Log($"【摇杆操作】结束拖拽 - 最终目标位置: {pointM}");
    }

    private void OnDrag(Vector2 localPos)
    {
        // 更新手指当前位置（B点）
        pointB = Camera.main.ScreenToWorldPoint(Input.mousePosition);

        // 计算Y轴方向的移动，X轴保持为出生点X坐标
        // 只计算Y轴的偏移量：M.y = K.y + (B.y - A.y)，M.x = spawnX（固定）
        float targetY = pointK.y + (pointB.y - pointA.y);
        pointM = new Vector2(spawnX, targetY);

        // 计算移动方向（只在Y轴方向）
        if (playerMoveComp != null)
        {
            Vector2 currentPos = playerMoveComp.transform.position;

            // 只计算Y轴方向的移动向量
            Vector2 directionVector = new Vector2(0, pointM.y - currentPos.y);
            moveDir = directionVector.normalized;
            moveDis = Mathf.Abs(pointM.y - currentPos.y);

            // 设置目标位置为M点（Y可变，X固定为出生点X坐标）
            playerMoveComp.SetTargetPosition(pointM);

            Debug.Log($"【摇杆操作】拖拽中 - Y轴偏移: {pointB.y - pointA.y:F2}, 目标Y: {targetY:F2}, 目标X(固定): {spawnX:F2}");
        }

        // 更新摇杆显示
        // 移动底盘和摇杆一起移动
        transform.position = UIMgr.Instance.GetUICamera().ScreenToWorldPoint(Input.mousePosition);
        transform.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y, 0);
        
        // 焦点位置保持在中心
        focusPoint.localPosition = Vector3.zero;
    }
}
