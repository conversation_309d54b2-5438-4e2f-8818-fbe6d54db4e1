﻿using System.Linq;

using Thing;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的枪的执行器
    /// </summary>
    public class ActorGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个玩家角色
        /// </summary>
        public ActorThing Actor => GunThing.Owner as ActorThing;

        /// <inheritdoc />
        protected override void OnAfterShoot()
        {
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayerMove.MoveDuration = 0;
        }

        /// <inheritdoc />
        public override void ClearShooter()
        {
            // 从所有怪物的 被锁列表 中移除该执行者
            _ = SingletonMgr.Instance.BattleMgr.Monsters.Select(enemy =>
            {
                enemy.LockedByShooters.Remove(this);
                return enemy;
            }).ToList();
        }
    }
}