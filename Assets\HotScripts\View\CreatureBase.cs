﻿// ReSharper disable IdentifierTypo

using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Spine.Unity;

using Thing;

using UnityEngine;

namespace View
{
	/// <summary>
	/// 生物界面(玩家、怪物、宠物)
	/// </summary>
	public abstract class CreatureBase : ThingBehaviour
	{
		/// <summary>
		/// 生物物件(数据)
		/// </summary>
		public CreatureThing CreatureThing => Thing as CreatureThing;

		/// <summary>
		/// 生物的刚体
		/// </summary>
		public Rigidbody2D Rigidbody { get; set; }

		public override void Awake()
		{
            base.Awake();
            
			Rigidbody = gameObject.GetOrAddComponent<Rigidbody2D>();
			Rigidbody.isKinematic = true;
			Rigidbody.gravityScale = 0;
		}

        /// <inheritdoc />
        public override void DoSyncToThing()
        {
            base.DoSyncToThing();

            #region 覆盖区域

            if (CreatureThing != null)
            {
                var circularArea2D = new CircularArea2D
                {
                    Center = transform.position, Radius = CreatureThing.TotalProp_Radius,
                };

                CreatureThing.Area2D.Replace(circularArea2D);
            }

            #endregion
        }
	}
}
