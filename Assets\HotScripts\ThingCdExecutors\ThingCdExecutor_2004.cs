﻿// ReSharper disable InconsistentNaming

using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     围击炸弹(怪物的枪)
    /// </summary>
    public class ThingCdExecutor_2004 : MonsterGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 物件的射程
            double gunRange = Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            if (gunRange <= 0)
            {
                Debug.LogWarning($"射程为0，无法发射子弹");
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                Debug.LogWarning($"没有找到攻击目标，无法发射子弹");
                return;
            }

            // 攻击距离还够不到目标，就不发子弹了。
            if (distanceEnemy.Distance > gunRange)
            {
                Debug.LogWarning($"攻击距离不够，无法发射子弹: 距离={distanceEnemy.Distance}, 射程={gunRange}");
                return;
            }

            // 调用基类的DoShoot方法(MonsterGunCdExecutor)播放攻击动画
            Debug.Log($"准备调用基类MonsterGunCdExecutor.DoShoot方法以播放攻击动画");
            base.DoShoot(token).Forget();
            Debug.Log($"已调用基类的DoShoot方法");

            await UniTask.SwitchToMainThread(token);
            Debug.Log($"已切换到主线程");

            // 怪物已销毁，不发子弹
            if (MonsterThing == null)
            {
                Debug.LogWarning($"怪物已销毁，无法发射子弹");
                return;
            }

            Debug.Log($"准备发射围击炸弹");
            
            // 目标位置
            Vector3 targetPos = distanceEnemy.Thing2.Position;

            Vector3 bulletEndPos = Actor.Position;
            long bulletCount = Thing.GetTotalLong(PropType.BurstBulletCountList).FirstOrDefault();
            float bulletBornDistanceToActor =
                (float)Thing.GetTotalDouble(PropType.BulletBornDistanceToActor).FirstOrDefault();

            for (int i = 0; i < bulletCount; i++)
            {
                Vector3 offset =
                    (Vector3.right * bulletBornDistanceToActor).RotateAround(Vector3.forward,
                        RandomNum.RandomFloat(0, 360));
                Vector3 bulletPos = bulletEndPos + offset;

                // 创建炸弹
                BulletThing bullet = MonsterThing.CreateBullet(this, null, targetPos, 0, 0, 0, 0);

                BornSiegeBomb bornSiegeBomb = new BornSiegeBomb
                {
                    Bullet = bullet, BornPosition = bulletPos, TargetPosition = targetPos
                };

                MessageBroker.Default.Publish(bornSiegeBomb);
            }
            
            Debug.Log($"[ThingCdExecutor_2004] 怪物围击炸弹DoShoot结束");
        }
    }
}