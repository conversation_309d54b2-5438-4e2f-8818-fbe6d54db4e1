﻿// ReSharper disable InconsistentNaming

using System.Linq;

using Apq.ChangeBubbling;
using Apq.Unity3D.UnityHelpers;

using Props;

using UnityEngine;

using View;

using X.PB;

namespace Thing
{
    /// <summary>
    ///     生物物件(数据)
    /// </summary>
    public abstract class CreatureThing : ThingBase
    {
        protected CreatureThing(object key = default, IBubbleNode parent = null) : base(key, parent)
        {
            NextImpactEnemyTime = new BubblingList<float>(nameof(NextImpactEnemyTime), this);
        }

        /// <summary>
        ///     生物界面覆盖的圆形区域
        /// </summary>
        public CircularArea2D CircularArea2D
        {
            get
            {
                if (Area2D != null && Area2D.Any() && Area2D.FirstOrDefault() is CircularArea2D circularArea)
                {
                    return circularArea;
                }

                // 如果Area2D为空或不包含CircularArea2D，创建一个默认的
                var defaultArea = new CircularArea2D { Center = (Vector2)Position, Radius = TotalProp_Radius };
                
                // 如果Area2D有效，添加这个默认值
                if (Area2D != null)
                {
                    Area2D.Clear(); // 清除可能的空或无效元素
                    Area2D.Add(defaultArea);
                    Debug.Log($"已为生物创建新的CircularArea2D: 物件={ThingType}, 中心={Position}, 半径={TotalProp_Radius}");
                }
                
                return defaultArea;
            }
        }

        /// <summary>
        ///     下次可以撞击敌人的时间
        /// </summary>
        public BubblingList<float> NextImpactEnemyTime { get; }

        /// <summary>
        ///     计算生物出生位置(依次替补)
        /// </summary>
        public override Vector3 CalcPositionInit()
        {
            // Vector3 rtn = Vector3.zero;
        
            Vector2? rtn = SingletonMgr.Instance.BattleMgr.MapMgr.Map.CalcPositionNear(
                new CircularArea2D { Center = PositionInit, Radius = TotalProp_Radius });
            return rtn ?? Vector3.zero;
        
            // List<CircularArea2D> circularAreas = PositionInit
            //     .Select(x => new CircularArea2D { Center = x, Radius = TotalProp_Radius }).ToList();
            //
            // foreach (CircularArea2D circularArea in circularAreas)
            // {
            //     rtn = circularArea.Center;
            //     List<ObstacleBase> obstacles = SingletonMgr.Instance.BattleMgr.MapMgr.Map.FindObstacles(circularArea);
            //     if (obstacles is { Count: > 0 } && obstacles.Any(x => x.ObstacleThing.TerrainType > TerrainType.None))
            //     {
            //         continue;
            //     }
            //
            //     break;
            // }
        
            // return rtn;
        }

        /// <summary>
        ///     重新计算 枪、生物 的 提升、总属性
        /// </summary>
        public virtual void ReCalcHoistAndTotal_All()
        {
            // 枪提取属性(应用到枪)
            Guns.ToList().ForEach(g => g.PickProps());
            // 生物提取属性(应用到生物)
            PickProps();

            // 枪生成携带的Buff(加载Buff的附着属性)
            _ = Guns.Select(g =>
            {
                g.ReGenCarriedBuffs();
                return g;
            }).ToList();

            // 枪计算提升
            Guns.SelectMany(g => g.HoistedProps).ToList()
                .ForEach(h => h.ReHoist());
            // 生物计算提升
            _ = HoistedProps.Select(h => h.ReHoist()).ToList();

            // 枪计算总属性(含携带的Buff)
            Guns.ToList().ForEach(g => g.ReCalcTotalProps());
            // 生物计算总属性(含携带的Buff)
            ReCalcTotalProps();
        }

        /// <summary>
        ///     计算提升系数
        /// </summary>
        /// <param name="hoistProp">提升类属性</param>
        public override double CalcHoistCoe(CommonProp hoistProp)
        {
            return hoistProp.HoistCoeMethod switch
            {
                HoistCoeMethod.LossHpPct => 1 - (Hp.Value / (GetTotalDouble(PropType.MaxHp)?.FirstOrDefault() ?? 100.0)),
                _ => 1
            };
        }
    }
}