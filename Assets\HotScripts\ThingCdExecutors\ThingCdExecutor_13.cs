// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using CsvTables;
using DataStructure;

using Google.Protobuf;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     角色阵营的抛物线子弹射击 - ShootMethod:13
    /// </summary>
    public class ThingCdExecutor_13 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            //Debug.Log($"88888 抛物线射击开始 - Actor:{Actor?.GetHashCode()} Thing:{Thing?.GetHashCode()} ShootMethod:13");
            
            // 使用新的目标选择逻辑
            var (targetEnemy, targetPos) = FindParabolicTarget();

            // 确保有目标位置，即使没有找到敌人也要有默认位置
            //Debug.Log($"88888 抛物线射击目标确定 - Actor:{Actor?.GetHashCode()} Target:{targetEnemy?.GetHashCode()} TargetPos:{targetPos}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                //Debug.Log($"88888 抛物线射击失败 - Actor为空");
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向始终是向Y轴正方向
                Vector3 shootDir = Vector3.up;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                //Debug.Log($"88888 抛物线射击配置 - ShootTimes:{shootTimes} DelayCount:{burstDelayList.Count} BulletCountList:{string.Join(",", burstBulletCountList)} AnglesIds:{string.Join(",", burstAnglesIds)} ShootDir:{shootDir}");

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        //Debug.Log($"88888 抛物线射击跳过 - 第{i}轮缺少子弹数量配置");
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    //Debug.Log($"88888 抛物线射击启动第{i}轮 - Delay:{x}s BulletQty:{bulletQty} TargetPos:{targetPos} AngleId:{burstAnglesIds.IndexOf_ByCycle(i)} (轮次间延时:{x}s, 轮内{bulletQty}颗子弹同时发射)");

                    // BurstDelayList用于不同轮次之间的延时，同一轮内的多颗子弹同时发射
                    BurstOne(cts_Skill.Token, (float)x, targetEnemy, targetPos, shootDir, bulletQty,
                            burstAnglesIds.IndexOf_ByCycle(i), burstDelayList)
                        .Forget();
                    return i;
                }).ToList();
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 抛物线射击异常 - {ex.Message} StackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算抛物线子弹的目标坐标
        /// </summary>
        /// <param name="target">目标敌人</param>
        /// <returns>计算后的目标坐标</returns>
        private Vector3 CalculateParabolicTarget(ThingBase target)
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            
            // 枪的射程
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            
            // 基础终点：角色Y坐标+8
            float baseTargetY = actorPos.y + 8f;
            
            // 目标怪物的Y坐标
            float monsterY = target.Position.y;
            
            // 终点Y坐标：在角色Y+8和目标怪物Y之间
            float targetY = Mathf.Max(baseTargetY, monsterY);
            
            // 限制在射程范围内
            float maxY = actorPos.y + gunRange;
            targetY = Mathf.Min(targetY, maxY);
            
            // 终点坐标：X轴保持角色当前X坐标，Y轴使用计算的目标Y
            return new Vector3(actorPos.x, targetY, actorPos.z);
        }

        /// <summary>
        /// 查找最适合的抛物线攻击目标
        /// </summary>
        /// <returns>目标怪物和目标坐标</returns>
        private (ThingBase target, Vector3 targetPos) FindParabolicTarget()
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            float minTargetY = actorPos.y + 8f; // 最小目标Y坐标
            float maxTargetY = actorPos.y + gunRange; // 最大目标Y坐标

            //Debug.Log($"88888 抛物线目标搜索 - ActorPos:{actorPos} GunRange:{gunRange} TargetYRange:[{minTargetY}, {maxTargetY}]");

            // 获取所有活跃的怪物
            var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && m.ThingBehaviour != null && m.ThingBehaviour.gameObject.activeInHierarchy)
                .ToList();

            //Debug.Log($"88888 抛物线目标搜索 - 总怪物数:{allMonsters.Count}");

            if (allMonsters.Count == 0)
            {
                // 没有怪物，使用GunRange最大值作为默认目标位置
                Vector3 defaultTarget = new Vector3(actorPos.x, maxTargetY, actorPos.z);
                //Debug.Log($"88888 抛物线目标搜索 - 无怪物，使用GunRange最大值目标:{defaultTarget} (Y:{maxTargetY} = 角色Y:{actorPos.y} + GunRange:{gunRange})");
                return (null, defaultTarget);
            }

            // 过滤满足Y坐标条件的怪物
            var validMonsters = allMonsters
                .Where(m => m.Position.y >= minTargetY && m.Position.y <= maxTargetY)
                .ToList();

            //Debug.Log($"88888 抛物线目标搜索 - Y范围内怪物数:{validMonsters.Count}");

            ThingBase bestTarget = null;
            float minDistance = float.MaxValue;

            if (validMonsters.Count > 0)
            {
                // 从满足Y坐标条件的怪物中选择与角色Y坐标距离最近的
                foreach (var monster in validMonsters)
                {
                    float yDistance = Mathf.Abs(monster.Position.y - actorPos.y);
                    if (yDistance < minDistance)
                    {
                        minDistance = yDistance;
                        bestTarget = monster;
                    }
                }
                
                //Debug.Log($"88888 抛物线目标搜索 - Y范围内最近怪物:{bestTarget?.GetHashCode()} YDistance:{minDistance} MonsterPos:{bestTarget?.Position}");
            }
            else
            {
                // 没有满足Y条件的怪物，从所有怪物中选择Y坐标距离最近的
                foreach (var monster in allMonsters)
                {
                    float yDistance = Mathf.Abs(monster.Position.y - actorPos.y);
                    if (yDistance < minDistance)
                    {
                        minDistance = yDistance;
                        bestTarget = monster;
                    }
                }
                
                //Debug.Log($"88888 抛物线目标搜索 - 全范围最近怪物:{bestTarget?.GetHashCode()} YDistance:{minDistance} MonsterPos:{bestTarget?.Position}");
            }

            Vector3 targetPos;
            if (bestTarget != null)
            {
                // 计算目标坐标：X为角色X坐标，Y为限制在范围内的怪物Y坐标
                float targetY = Mathf.Clamp(bestTarget.Position.y, minTargetY, maxTargetY);
                targetPos = new Vector3(actorPos.x, targetY, actorPos.z);
                
                //Debug.Log($"88888 抛物线目标搜索 - 最终目标:{bestTarget.GetHashCode()} 原始Y:{bestTarget.Position.y} 目标Y:{targetY} 目标坐标:{targetPos}");
            }
            else
            {
                // 没有找到怪物，使用GunRange最大值作为默认目标位置
                targetPos = new Vector3(actorPos.x, maxTargetY, actorPos.z);
                //Debug.Log($"88888 抛物线目标搜索 - 未找到怪物，使用GunRange最大值目标:{targetPos}");
            }

            return (bestTarget, targetPos);
        }

        /// <summary>
        ///     发射一轮垂直轰炸子弹
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">目标位置（用于记录角色Y坐标）</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        /// <param name="burstDelayList">子弹间隔列表</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId, List<double> burstDelayList)
        {
            try
            {
                //Debug.Log($"88888 垂直轰炸BurstOne开始 - Delay:{delay}s BulletQty:{bulletQty}");
                
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    //Debug.Log($"88888 垂直轰炸BurstOne取消 - Token已取消");
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                // 获取子弹配置的Remark参数 A;B;C
                int bulletId = (int)Thing.GetTotalLong(PropType.BulletId).FirstOrDefault();
                var bulletCfg = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Pb.CSVTable.FirstOrDefault(x => x.BulletId == bulletId);
                if (bulletCfg == null)
                {
                    //Debug.LogError($"88888 垂直轰炸失败 - 找不到子弹配置 BulletId:{bulletId}");
                    return;
                }

                // 解析Remark参数 A;B;C
                string[] remarkParams = bulletCfg.Remark?.Split(';') ?? new string[0];
                if (remarkParams.Length < 3)
                {
                    //Debug.LogError($"88888 垂直轰炸失败 - Remark参数不足 Remark:{bulletCfg.Remark} 需要格式:A;B;C");
                    return;
                }

                if (!float.TryParse(remarkParams[0], out float paramA) ||
                    !float.TryParse(remarkParams[1], out float paramB) ||
                    !float.TryParse(remarkParams[2], out float paramC))
                {
                    //Debug.LogError($"88888 垂直轰炸失败 - Remark参数解析失败 Remark:{bulletCfg.Remark}");
                    return;
                }

                Vector3 actorPos = Actor.Position;
                float targetY = actorPos.y; // 记录角色当前Y坐标作为子弹下落目标

                //Debug.Log($"88888 垂直轰炸参数 - A:{paramA} B:{paramB} C:{paramC} ActorPos:{actorPos} TargetY:{targetY}");

                for (int z = 0; z < bulletQty; z++)
                {
                    if (token.IsCancellationRequested)
                    {
                        //Debug.Log($"88888 垂直轰炸中断 - 第{z}颗子弹创建时Token已取消");
                        return;
                    }

                    // 计算子弹初始位置
                    // X坐标：角色X + B * z（B值间隔，以角色前方为正向）
                    // Y坐标：角色Y + A（初始高度）
                    float bulletX = actorPos.x + paramB * z;
                    float bulletY = actorPos.y + paramA;
                    Vector3 bulletStartPos = new Vector3(bulletX, bulletY, actorPos.z);

                    // 创建垂直轰炸子弹
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 设置TrackPosition为角色当前Y坐标位置（子弹下落目标）
                    Vector3 bulletTargetPos = new Vector3(bulletX, targetY, actorPos.z);

                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, bulletTargetPos, 0,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    if (bullet != null)
                    {
                        // 设置子弹的初始位置和目标位置
                        bullet.AttackBaseDirFollowThing = attackBaseDirFollowThing;
                        bullet.TrackPosition = bulletTargetPos;
                        bullet.Position = bulletStartPos;
                        
                        // 设置垂直向下的移动方向
                        bullet.MoveDirection_Straight.Value = Vector3.down;

                        // 添加停留时间参数到子弹属性中
                        bullet.GetOrAddProp(PropType.Remark).StrValues.Clear();
                        bullet.GetOrAddProp(PropType.Remark).StrValues.Add(paramC.ToString());

                        //Debug.Log($"88888 垂直轰炸子弹创建成功 - 第{z}颗 StartPos:{bulletStartPos} TargetPos:{bulletTargetPos} StayTime:{paramC}s");

                        MessageBroker.Default.Publish(new BornBullet { Bullet = bullet });
                    }
                    else
                    {
                        //Debug.LogError($"88888 垂直轰炸子弹创建失败 - 第{z}颗 CreateBullet返回null");
                    }
                }
                
                //Debug.Log($"88888 垂直轰炸BurstOne完成 - 同时发射了{bulletQty}颗子弹");
            }
            catch (OperationCanceledException)
            {
                //Debug.Log($"88888 垂直轰炸BurstOne操作取消");
                throw;
            }
            catch (MissingReferenceException ex)
            {
                //Debug.LogError($"88888 垂直轰炸BurstOne引用丢失 - {ex.Message}");
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 垂直轰炸BurstOne异常 - {ex.Message} StackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算角度目标位置
        /// </summary>
        /// <param name="baseTrackPos">0度角的目标位置</param>
        /// <param name="angle">角度</param>
        /// <returns>计算后的目标位置</returns>
        private Vector3? CalculateAngleTargetPosition(Vector3? baseTrackPos, float angle)
        {
            if (!baseTrackPos.HasValue)
            {
                //Debug.Log($"99999 角度目标计算 - baseTrackPos为空，返回null");
                return null;
            }

            // 0度角直接返回原目标位置
            if (Mathf.Abs(angle) < 0.01f)
            {
                //Debug.Log($"99999 角度目标计算 - Angle:{angle} 是0度角，直接返回原目标位置:{baseTrackPos.Value}");
                return baseTrackPos;
            }

            Vector3 actorPos = Actor.Position;
            Vector3 zeroAngleTarget = baseTrackPos.Value;
            
            // 计算0度角坐标点到技能发射者的距离（作为半径）
            float radius = Vector3.Distance(actorPos, zeroAngleTarget);
            
            // 将角度转换为弧度
            float angleRad = angle * Mathf.Deg2Rad;
            
            // 以技能发射者为圆心，半径为上述距离，计算该角度对应的圆弧上的点
            // 假设0度角是向Y轴正方向，角度逆时针增加
            Vector3 direction = new Vector3(Mathf.Sin(angleRad), Mathf.Cos(angleRad), 0f);
            Vector3 targetPos = actorPos + direction * radius;
            
            //Debug.Log($"99999 角度目标计算 - Angle:{angle}° ActorPos:{actorPos} ZeroAngleTarget:{zeroAngleTarget} Radius:{radius:F2} Direction:{direction} CalculatedTarget:{targetPos}");
            
            return targetPos;
        }
    }
}