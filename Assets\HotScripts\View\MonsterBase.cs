// ReSharper disable InconsistentNaming

using System.Linq;

using Apq.Unity3D.Extension;

using HotScripts;

using Thing;

using UnityEngine;
using UnityEngine.Events;
using Spine.Unity;

using X.PB;

namespace View
{
    /// <summary>
    /// 怪物
    /// </summary>
    public class MonsterBase : CreatureBase
    {
        /// <summary>
        /// 怪物物件(数据)
        /// </summary>
        public MonsterThing MonsterThing => CreatureThing as MonsterThing;

        /// <summary>
        /// 移动组件
        /// </summary>
        public MonsterMoveAI MonsterMoveAI { get; set; }

        /// <summary>
        /// 怪物AI组件
        /// </summary>
        public MonsterAI MonsterAI { get; set; }

        /// <summary>
        /// 寻路组件
        /// </summary>
        public NavAgent NavAgent { get; set; }

        // 动画播放状态跟踪
        private string _currentAnimationName;
        private float _lastAnimationStartTime;
        private float _minAnimationDuration = 1.0f; // 增加动画最小持续时间
        private bool _isPlayingMoveAnimation = false; // 标记是否正在播放移动动画
        private float _moveAnimationDuration = 0f; // 移动动画的持续时间

        /// <inheritdoc/>
        public override Vector3 CalcDir_Straight()
        {
            // 安全检查：确保战斗管理器和Actor都存在
            if (SingletonMgr.Instance == null || SingletonMgr.Instance.BattleMgr == null || 
                SingletonMgr.Instance.BattleMgr.Actor == null)
            {
                return Vector3.down;
            }
            
            // X轴朝向玩家的方向
            var dirX = new Vector3(SingletonMgr.Instance.BattleMgr.Actor.Position.x - transform.position.x, 0)
                .normalized;
            // Y轴朝向玩家的方向
            var dirY = new Vector3(0, SingletonMgr.Instance.BattleMgr.Actor.Position.y - transform.position.y)
                .normalized;

            // 枪
            var gun = MonsterThing?.Guns?.Value;
            if (gun == null) return Vector3.down;

            // 枪的射程
            var gunRange = gun.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            //if (gunRange <= 0) return Vector3.down;

            // 怪物的半径
            var monsterRadius = CreatureThing?.TotalProp_Radius ?? 0;
            // 角色的半径
            var actorRadius = SingletonMgr.Instance.BattleMgr.Actor.TotalProp_Radius;

            // 与角色的距离
            var distance = SingletonMgr.Instance.BattleMgr.Actor.Position.CalcDistance2D_SolidCircleToSolidCircle(
                actorRadius, transform.position, monsterRadius);

            // 与玩家的X轴距离
            var distanceX = System.Math.Abs(SingletonMgr.Instance.BattleMgr.Actor.Position.x - transform.position.x)
                            - actorRadius - monsterRadius;
            // 与玩家的Y轴距离
            var distanceY = System.Math.Abs(SingletonMgr.Instance.BattleMgr.Actor.Position.y - transform.position.y)
                            - actorRadius - monsterRadius;

            // 能打到,不动了
            if (distance <= gunRange)
            {
                return Vector3.zero;
            }

            // // 1、要靠近了
            // if (distance <= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance ||
            //     // 2、X、Y轴都太远
            //     (distanceX >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance &&
            //      distanceY >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance))
            // {
            //     // 向玩家方向移动
            //     var dir = SingletonMgr.Instance.BattleMgr.Actor.Position - transform.position;
            //     var dir_1 = dir.normalized;
            //     return dir_1;
            // }
            //
            // // 横向仍太远，先收拢
            // if (distanceX >= SingletonMgr.Instance.GlobalMgr.CONST_Monster_MoveDown_MinDistance)
            // {
            //     return dirX;
            // }

            // 还远着,Y轴朝向玩家的方向
            return dirY;
        }

        public override void Awake()
        {
            base.Awake();
            
            MonsterAI = gameObject.GetOrAddComponent<MonsterAI>();
            
            // 添加对InitMoveAI的调用
            InitMoveAI();
        }

        // 辅助方法：获取可用的动画列表
        private string GetAvailableAnimations(SkeletonAnimation skeAni)
        {
            if (skeAni == null || skeAni.skeleton == null || skeAni.skeleton.Data == null) 
                return "<无动画数据>";
                
            var animations = skeAni.skeleton.Data.Animations;
            return string.Join(", ", animations.Select(a => a.Name));
        }

        // 判断当前怪物是否为MoveType=8数9的特殊怪物
        private bool IsMoveType8Or9Monster()
        {
            if (MonsterThing == null || MonsterThing.CsvRow_BattleBrushEnemy == null)
                return false;
                
            int moveType = MonsterThing.CsvRow_BattleBrushEnemy.MoveType;
            return moveType == 8 || moveType == 9;
        }
        
        // 检查当前怪物是否为MoveType=10/11/12类型
        private bool IsMoveType10To12Monster()
        {
            if (MonsterThing == null || MonsterThing.CsvRow_BattleBrushEnemy == null)
                return false;
                
            int moveType = MonsterThing.CsvRow_BattleBrushEnemy.MoveType;
            return moveType >= 10 && moveType <= 12;
        }
        
        // 检查并可能进行动画替换的方法
        private string CheckAndReplaceAnimation(string originalAniName)
        {
            // 安全检查
            if (string.IsNullOrEmpty(originalAniName))
                return "idle01"; // 默认动画
                
            // 对于MoveType=8和9的怪物，如果传入idle01，则替换为move01
            if (originalAniName == "idle01" && IsMoveType8Or9Monster())
            {
                // 检查是否存在move01动画
                if (SkeAni != null && SkeAni.skeleton != null && SkeAni.skeleton.Data != null && 
                    SkeAni.skeleton.Data.FindAnimation("move01") != null)
                {
                    //Debug.Log($"1111111 MoveType={MonsterThing?.CsvRow_BattleBrushEnemy?.MoveType}的怪物将idle01动画替换为move01动画");
                    return "move01";
                }
            }
            
            return originalAniName;
        }

        // 添加初始化方法
        private void InitMoveAI()
        {
            // 确保NavAgent已初始化
            if (NavAgent == null)
            {
                NavAgent = gameObject.GetComponent<NavAgent>();
                if (NavAgent == null)
                {
                    NavAgent = gameObject.AddComponent<NavAgent>();
                }
            }
            
            // 检查是否为MoveType 12的怪物
            if (MonsterThing != null && MonsterThing.CsvRow_BattleBrushEnemy != null)
            {
                if (MonsterThing.CsvRow_BattleBrushEnemy.MoveType == 12)
                {
                    //Debug.Log($"检测到MoveType 12怪物，准备添加MonsterMoveAI01组件");
                    
                    // 先销毁旧的移动AI组件
                    if (MonsterMoveAI != null)
                    {
                        //Debug.Log($"销毁现有的MonsterMoveAI组件");
                        Destroy(MonsterMoveAI);
                        MonsterMoveAI = null;
                    }
                    
                    // 获取或添加MonsterMoveAI01组件
                    MonsterMoveAI01 newAI = GetComponent<MonsterMoveAI01>();
                    if (newAI == null)
                    {
                        //Debug.Log($"添加新的MonsterMoveAI01组件");
                        newAI = gameObject.AddComponent<MonsterMoveAI01>();
                    }
                    
                    // 设置NavAgent
                    newAI.NavAgent = NavAgent;
                    
                    //Debug.Log($"MoveType 12怪物初始化完成，已添加MonsterMoveAI01组件");
                }
                else
                {
                    // 非MoveType 12的怪物使用默认的移动AI
                    MonsterMoveAI01 existingAI01 = GetComponent<MonsterMoveAI01>();
                    if (existingAI01 != null)
                    {
                        //Debug.Log($"非MoveType 12怪物，移除MonsterMoveAI01组件");
                        Destroy(existingAI01);
                    }
                    
                    // 确保有标准的MonsterMoveAI组件
                    if (MonsterMoveAI == null)
                    {
                        //Debug.Log($"添加标准MonsterMoveAI组件");
                        MonsterMoveAI = gameObject.AddComponent<MonsterMoveAI>();
                    }
                    
                    MonsterMoveAI.NavAgent = NavAgent;
                    
                    if (MonsterThing != null) {
                        MonsterThing.UpdateSkillRange();
                    }
                }
            }
        }
        
        public override void Update()
        {
            base.Update();
            
            if (CreatureThing == null) return;
            
            // 每帧检查MoveType=8和9的怪物是否正在播放idle01动画
            if (IsMoveType8Or9Monster() && SkeAni != null && SkeAni.state != null)
            {
                var currentTrack = SkeAni.state.GetCurrent(0);
                if (currentTrack != null && currentTrack.Animation != null && currentTrack.Animation.Name == "idle01")
                {
                    //Debug.Log($"1111111 Update中发现MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物正在播放idle01动画，强制替换为move01");
                    SkeAni.state.SetAnimation(0, "move01", true);
                }
            }
            
            // 怪物的半径
            var monsterRadius = CreatureThing.TotalProp_Radius;
            // 角色的半径
            var actorRadius = SingletonMgr.Instance.BattleMgr.Actor.TotalProp_Radius;

            // 与角色的距离
            var distance = SingletonMgr.Instance.BattleMgr.Actor.Position.CalcDistance2D_SolidCircleToSolidCircle(
                actorRadius, transform.position, monsterRadius);

            if (MonsterThing.NextImpactEnemyTime.Value <= Time.time)
            {
                if (distance <= float.Epsilon)
                {
                    Debug.Log($"77777 怪物碰撞角色: 距离={distance}, 怪物半径={monsterRadius}, 角色半径={actorRadius}");
                    
                    // // 撞击的声音
                    // AudioPlayer.Instance.PlaySound(MonsterThing.GetTotalString(PropType.HitSound).FirstOrDefault())
                    //     .Forget();

                    MonsterThing.NextImpactEnemyTime.Value =
                        Time.time + (float)MonsterThing.CsvRow_BattleBrushEnemy.DamageAdd;

                    Debug.Log($"77777 怪物碰撞设置下次伤害时间: {MonsterThing.NextImpactEnemyTime.Value}");

                    // 获取当前Helper调试模式状态
                    int actorDefenseMode = Helper.ActorDefenseMode;
                    int monsterAttackMode = Helper.MonsterAttackMode;
                    Debug.Log($"77777 怪物碰撞Helper调试模式: ActorDefenseMode={actorDefenseMode}, MonsterAttackMode={monsterAttackMode}");

                    // 计算伤害量
                    var damage = Helper.CalcDamage(MonsterThing.Guns.FirstOrDefault(),
                        SingletonMgr.Instance.BattleMgr.Actor);

                    Debug.Log($"77777 怪物碰撞伤害计算结果: damage={damage}");

                    // 获取角色受击前的生命值
                    double hpBefore = SingletonMgr.Instance.BattleMgr.Actor.Hp.Value;
                    Debug.Log($"77777 怪物碰撞受击前角色生命值: {hpBefore}");

                    // 接受伤害
                    SingletonMgr.Instance.BattleMgr.Actor.TakeHit(MonsterThing, damage);
                    
                    // 获取角色受击后的生命值
                    double hpAfter = SingletonMgr.Instance.BattleMgr.Actor.Hp.Value;
                    Debug.Log($"77777 怪物碰撞受击后角色生命值: {hpAfter}, 实际伤害={hpBefore - hpAfter}");
                }
            }
            SetSpineSortingOrderBySelfPosition();
        }
        
        /// <summary>
        /// 开始怪物移动
        /// </summary>
        public void StartMove()
        {
            if (MonsterMoveAI != null)
            {
                MonsterMoveAI.StartMove();
            }
        }

        // 重写PlayAnimation方法，增加日志记录
        public override void PlayAnimation(string aniName, bool loop = false, bool isBackToIdle = true, float timeScale = 1f)
        {
            // 强制检查并可能替换动画名称
            aniName = CheckAndReplaceAnimation(aniName);
            
            // 检查SkeAni组件
            if (SkeAni == null)
            {
                //Debug.LogWarning($"怪物 {gameObject.name} SkeAni为空，尝试重新获取");
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
                
                if (SkeAni == null)
                {
                    //Debug.LogError($"怪物 {gameObject.name} 无法获取SkeAni组件，请检查预制体是否包含SkeletonAnimation组件");
                }
            }
            
            // 检查该动画是否存在
            if (SkeAni.skeleton != null && SkeAni.skeleton.Data != null)
            {
                bool hasAnimation = SkeAni.skeleton.Data.FindAnimation(aniName) != null;
                
                if (!hasAnimation)
                {
                    string availableAnims = GetAvailableAnimations(SkeAni);
                    //Debug.LogWarning($"动画 {aniName} 不存在, 可用动画: {availableAnims}");
                    
                    // 尝试播放idle01或move01
                    if (SkeAni.skeleton.Data.FindAnimation("idle01") != null)
                    {
                        aniName = "idle01";
                    }
                    else if (SkeAni.skeleton.Data.FindAnimation("move01") != null)
                    {
                        aniName = "move01";
                    }
                    else
                    {
                        //Debug.LogError($"没有找到可用的替代动画，无法播放");
                        return;
                    }
                }
            }
            
            // 注意：我们已经在方法开始接着CheckAndReplaceAnimation进行了动画替换检查
            
            // 检查当前是否正在播放attack01动画
            var currentTrackEntry = SkeAni.state?.GetCurrent(0);
            bool isPlayingAttack = currentTrackEntry != null && 
                                 currentTrackEntry.Animation != null && 
                                 currentTrackEntry.Animation.Name == "attack01" &&
                                 !currentTrackEntry.IsComplete;
            
            // 如果当前正在播放attack01动画且尝试播放的不是attack01，检查是否要尊重攻击动画优先级
            if (isPlayingAttack && aniName != "attack01")
            {
                // 如果希望攻击动画播放完再播放新动画，可以在这里添加到动画队列
                if (currentTrackEntry.TimeScale > 0 && !currentTrackEntry.IsComplete)
                {
                    SkeAni.state.AddAnimation(0, aniName, loop, 0);
                    return; // 不立即播放新动画，让attack01先完成
                }
            }
            
            // 处理move01动画播放完整的增强逻辑
            if (aniName == "move01")
            {
                //Debug.Log($"1111111 尝试播放move01动画 当前状态: 正在播放={_isPlayingMoveAnimation}, 当前动画={_currentAnimationName}, 动画持续时间={_moveAnimationDuration}, 上次播放时间={_lastAnimationStartTime}, 已过时间={Time.time - _lastAnimationStartTime}");
                
                // 获取move01动画的持续时间
                if (_moveAnimationDuration <= 0 && SkeAni.skeleton != null && SkeAni.skeleton.Data != null)
                {
                    var moveAnim = SkeAni.skeleton.Data.FindAnimation("move01");
                    if (moveAnim != null)
                    {
                        _moveAnimationDuration = moveAnim.Duration;
                        // 确保最小持续时间不会超过动画本身的时长
                        _minAnimationDuration = Mathf.Min(_moveAnimationDuration * 0.9f, _minAnimationDuration);
                        //Debug.Log($"1111111 获取到move01动画持续时间: {_moveAnimationDuration}秒, 设置最小播放时间: {_minAnimationDuration}秒");
                    }
                    else
                    {
                        //Debug.Log($"1111111 警告: 未找到move01动画数据");
                    }
                }
                
                // 如果当前已经在播放move01动画，检查是否需要等待完成
                if (_isPlayingMoveAnimation && _currentAnimationName == "move01")
                {
                    // 检查当前动画播放进度
                    float elapsedTime = Time.time - _lastAnimationStartTime;
                    float animDuration = _moveAnimationDuration > 0 ? _moveAnimationDuration : _minAnimationDuration;
                    
                    // 获取当前轨道状态，用于日志
                    var currentTrack = SkeAni.state?.GetCurrent(0);
                    float trackTime = 0f;
                    float trackEnd = 0f;
                    string trackState = "未知";
                    
                    if (currentTrack != null && currentTrack.Animation != null)
                    {
                        trackTime = currentTrack.TrackTime;
                        trackEnd = currentTrack.AnimationEnd;
                        trackState = currentTrack.IsComplete ? "已完成" : "播放中";
                    }
                    
                    //Debug.Log($"1111111 move01动画播放状态: 已播放时间={elapsedTime:F3}秒, 动画总时长={animDuration:F3}秒, 当前进度={trackTime:F3}/{trackEnd:F3}, 状态={trackState}");
                    
                    // 确保动画至少播放一个完整周期，或至少播放了最小持续时间
                    if (elapsedTime < animDuration && elapsedTime < _minAnimationDuration)
                    {
                        //Debug.Log($"1111111 防止打断: move01动画播放时间不足, 已播放{elapsedTime:F3}秒 < 所需{Mathf.Min(animDuration, _minAnimationDuration):F3}秒");
                        return; // 不重新播放，让当前动画继续
                    }
                    
                    // 检查当前动画轨道状态
                    if (currentTrack != null && currentTrack.Animation != null && 
                        currentTrack.Animation.Name == "move01" && 
                        !currentTrack.IsComplete && currentTrack.TrackTime < currentTrack.AnimationEnd * 0.8f)
                    {
                        //Debug.Log($"1111111 防止打断: move01动画未到80%进度, 当前进度={currentTrack.TrackTime:F3}/{currentTrack.AnimationEnd:F3} ({currentTrack.TrackTime/currentTrack.AnimationEnd*100:F2}%)");
                        return; // 如果动画还没播放到80%，不打断它
                    }
                    
                    // 记录继续播放的原因
                    //Debug.Log($"1111111 允许更新move01动画: 原动画已经播放时间足够或播放进度足够");
                }
            }
            
            // 记录旧状态，用于日志
            string oldAnim = _currentAnimationName;
            bool wasPlayingMove = _isPlayingMoveAnimation;
            
            // 更新当前播放的动画名称和开始时间
            _currentAnimationName = aniName;
            _lastAnimationStartTime = Time.time;
            // 更新移动动画播放状态
            _isPlayingMoveAnimation = (aniName == "move01");
            
            //Debug.Log($"1111111 动画状态更新: {oldAnim}({wasPlayingMove}) -> {aniName}({_isPlayingMoveAnimation}), 时间标记={_lastAnimationStartTime}");
            
            if (SkeAni != null && SkeAni.state != null)
            {
                // 对于attack01动画，直接使用Spine API设置动画，避免被覆盖
                if (aniName == "attack01")
                {
                    //Debug.Log($"1111111 播放attack01动画: loop={loop}, timeScale={timeScale}");
                    var trackEntry = SkeAni.state.SetAnimation(0, aniName, loop);
                    if (trackEntry != null)
                    {
                        trackEntry.TimeScale = timeScale;
                        trackEntry.MixDuration = 0.1f; // 减少混合时间，更快切换到新动画
                        
                        if (!loop && isBackToIdle)
                        {
                            // 改为放完战斗动画之后播放移动动画，而不是待机动画
                            string nextAnimName = "move01";
                            if (SkeAni.skeleton.Data.FindAnimation(nextAnimName) == null) {
                                // 如果没有move01动画，则回退到待机动画
                                nextAnimName = "idle01";
                                if (SkeAni.skeleton.Data.FindAnimation(nextAnimName) == null) {
                                    nextAnimName = "idle"; // 备用idle动画名
                                }
                            }
                            
                            // 对于MoveType=8和9的怪物，特别检查和替换
                            if (IsMoveType8Or9Monster() && SkeAni.skeleton.Data.FindAnimation("move01") != null)
                            {
                                //Debug.Log($"1111111 MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物不添加idle动画，而是添加move01动画");
                                SkeAni.state.AddAnimation(0, "move01", true, 0);
                            }
                            else if (SkeAni.skeleton.Data.FindAnimation(nextAnimName) != null) 
                            {
                                SkeAni.state.AddAnimation(0, nextAnimName, true, 0);
                                //Debug.Log($"1111111 attack01动画后添加idle动画: {nextAnimName}");
                                //Debug.Log($"1111111 attack01动画后添加动画: {nextAnimName}");
                            }
                        }
                    }
                }
                else if (aniName == "move01")
                {
                    
                    //Debug.Log($"1111111 使用SetAnimation播放move01动画: loop={loop}, timeScale={timeScale}");
                    // 对于move01动画，我们直接使用Spine API设置，确保不被其他调用覆盖
                    var trackEntry = SkeAni.state.SetAnimation(0, "move01", loop);
                    if (trackEntry != null)
                    {
                        trackEntry.TimeScale = timeScale;
                        // 对于move01，使用更长的混合时间，使过渡更平滑
                        trackEntry.MixDuration = 0.25f;
                        //Debug.Log($"1111111 设置move01动画: trackEntry={trackEntry.TrackIndex}, duration={trackEntry.AnimationEnd:F3}秒, mixDuration={trackEntry.MixDuration:F3}秒");
                    }
                }
                else
                {
                    // 调用基类方法处理其他动画
                    //Debug.Log($"1111111 调用基类方法播放动画: {aniName}, loop={loop}, isBackToIdle={isBackToIdle}, timeScale={timeScale}");
                    base.PlayAnimation(aniName, loop, isBackToIdle, timeScale);
                }
            }
            else
            {
                //Debug.LogError($"SkeAni或state为空，无法播放动画");
            }
            
            // 检查动画是否成功播放
            if (SkeAni != null && SkeAni.state != null && SkeAni.state.GetCurrent(0) != null)
            {
                string currentAnim = SkeAni.state.GetCurrent(0).Animation?.Name;
                float currentTime = SkeAni.state.GetCurrent(0).TrackTime;
                float totalTime = SkeAni.state.GetCurrent(0).AnimationEnd;
                
                //Debug.Log($"1111111 播放后检查: 请求动画={aniName}, 实际播放={currentAnim}, 进度={currentTime:F3}/{totalTime:F3}");
                
                // 如果未成功设置attack01动画，再次尝试
                if (aniName == "attack01" && currentAnim != "attack01")
                {
                    //Debug.LogWarning($"1111111 attack01动画未成功设置，再次尝试强制设置");
                    SkeAni.state.SetAnimation(0, "attack01", loop);
                }
                
                // 检查move01动画是否被成功设置
                if (aniName == "move01" && currentAnim != "move01")
                {
                    //Debug.LogWarning($"1111111 move01动画未成功设置，实际播放的是{currentAnim}，尝试强制设置");
                    var trackEntry = SkeAni.state.SetAnimation(0, "move01", loop);
                    if (trackEntry != null)
                    {
                        trackEntry.TimeScale = timeScale;
                        //Debug.Log($"1111111 强制设置move01动画成功: timeScale={timeScale}");
                    }
                }
                
                // 检查MoveType=8和9的怪物是否正在播放idle01
                if (currentAnim == "idle01" && IsMoveType8Or9Monster())
                {
                    //Debug.LogWarning($"1111111 发现MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物在播放idle01，强制替换为move01");
                    var trackEntry = SkeAni.state.SetAnimation(0, "move01", true); // 总是循环播放
                    if (trackEntry != null)
                    {
                        trackEntry.TimeScale = 1f; // 使用标准速度
                        //Debug.Log($"1111111 强制将idle01替换为move01成功");
                    }
                }
            }
        }

        public override void Start()
        {
            base.Start();
            
            // 二次检查SkeAni组件，确保它被正确初始化
            if (SkeAni == null)
            {
                SkeAni = GetComponentInChildren<SkeletonAnimation>();
                if (SkeAni == null)
                {
                    //Debug.LogWarning($"怪物 {gameObject.name} 无法获取SkeAni组件，请检查预制体是否包含SkeletonAnimation组件");
                }
            }
            
            // 注册动画完成事件监听
            if (SkeAni != null && SkeAni.state != null)
            {
                SkeAni.state.Event += OnSpineEvent;
                SkeAni.state.Complete += OnSpineAnimationComplete;
                SkeAni.AnimationState.Start += OnAnimationStart; // 添加动画开始事件监听
                
                // 获取move01动画的持续时间
                if (SkeAni.skeleton != null && SkeAni.skeleton.Data != null)
                {
                    var moveAnim = SkeAni.skeleton.Data.FindAnimation("move01");
                    if (moveAnim != null)
                    {
                        _moveAnimationDuration = moveAnim.Duration;
                    }
                }
                
                // 对于MoveType=8如9的怪物，检查并确保初始动画不是idle01
                if (IsMoveType8Or9Monster() && SkeAni.AnimationName == "idle01")
                {
                    //Debug.Log($"1111111 Start中发现MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物初始动画是idle01，替换为move01");
                    PlayAnimation("move01", true);
                }
            }
        }
        
        // 动画事件回调
        private void OnSpineEvent(Spine.TrackEntry trackEntry, Spine.Event e)
        {
            if (trackEntry.Animation != null)
            {
                //Debug.Log($"1111111 动画事件: 动画={trackEntry.Animation.Name}, 事件={e.Data.Name}, 时间={trackEntry.TrackTime:F3}/{trackEntry.AnimationEnd:F3}");
            }
        }
        
        // 动画开始播放时的回调
        private void OnAnimationStart(Spine.TrackEntry trackEntry)
        {
            if (trackEntry == null || trackEntry.Animation == null) return;
            
            string animName = trackEntry.Animation.Name;
            //Debug.Log($"1111111 动画开始: 轨道={trackEntry.TrackIndex}, 动画={animName}, 循环={trackEntry.Loop}");
            
            // 对MoveType=8如9的怪物，如果要播放idle01，立即替换为move01
            if (animName == "idle01" && IsMoveType8Or9Monster())
            {
                //Debug.Log($"1111111 捕获到MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物试图播放idle01，直接替换为move01");
                SkeAni.state.SetAnimation(trackEntry.TrackIndex, "move01", trackEntry.Loop);
            }
        }
        
        // 动画完成回调
        private void OnSpineAnimationComplete(Spine.TrackEntry trackEntry)
        {
            if (trackEntry.Animation != null)
            {
                //Debug.Log($"1111111 动画完成: 动画={trackEntry.Animation.Name}, 播放时长={trackEntry.TrackTime:F3}秒/{trackEntry.AnimationEnd:F3}秒, 循环={trackEntry.Loop}");
                
                // 检查当前於完成的是否为idle01，如果是且怪物为MoveType=8数9，则立即播放move01
                if (trackEntry.Animation.Name == "idle01" && IsMoveType8Or9Monster())
                {
                    //Debug.Log($"1111111 MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物完成了idle01动画，强制切换为move01");
                    SkeAni.state.SetAnimation(trackEntry.TrackIndex, "move01", true);
                    return;
                }
                
                if (trackEntry.Animation.Name == "move01")
                {
                    // 记录移动动画已完成
                    bool oldState = _isPlayingMoveAnimation;
                    _isPlayingMoveAnimation = false;
                    //Debug.Log($"1111111 move01动画状态更新: 播放中={oldState} -> 已完成={_isPlayingMoveAnimation}");
                    
                    // 对于MoveType=8如9的怪物，如果move01完成了且不是循环模式，始终添加一个新的move01
                    if (!trackEntry.Loop && IsMoveType8Or9Monster())
                    {
                        //Debug.Log($"1111111 MoveType={MonsterThing.CsvRow_BattleBrushEnemy.MoveType}的怪物的move01完成，自动添加新的move01");
                        SkeAni.state.SetAnimation(trackEntry.TrackIndex, "move01", true);
                        return;
                    }
                    
                    // 如果不是循环模式，检查是否需要继续播放move01
                    if (!trackEntry.Loop)
                    {
                        // 在移动动画完成后，再次添加一个移动动画，确保持续移动
                        var currentTrack = SkeAni.state?.GetCurrent(0);
                        if (currentTrack != null && currentTrack.Animation != null && currentTrack.Animation.Name != "attack01")
                        {
                            // 只有当前不是在放攻击动画时才添加
                            //Debug.Log($"1111111 move01动画完成后自动连续播放move01");
                            SkeAni.state.SetAnimation(0, "move01", true);
                        }
                    }
                }
            }
        }
    }
}