﻿using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;
using RxEventsM2V;

using Thing;

using UniRx;

using View;
using Spine.Unity;

using UnityEngine;

namespace ThingCdExecutors
{
    /// <summary>
    ///     怪物阵营的枪的执行器
    /// </summary>
    public class MonsterGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个怪物
        /// </summary>
        public MonsterThing MonsterThing => GunThing.Owner as MonsterThing;

        /// <summary>
        ///     获取玩家角色(数据)
        /// </summary>
        protected ActorThing Actor => SingletonMgr.Instance.BattleMgr.Actor;

        /// <summary>
        ///     在发射子弹前播放攻击动画
        /// </summary>
        /// <param name="token">取消令牌</param>
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 怪物存在且有动画组件时，触发攻击动画
            if (MonsterThing != null)
            {
                // 确保ThingBehaviour不为空
                if (MonsterThing.ThingBehaviour == null)
                {
                    Debug.LogWarning($"怪物 {MonsterThing.Guid} 的ThingBehaviour为空，无法播放动画");
                    base.DoShoot(token).Forget();
                    return;
                }
                
                // 确保SkeAni组件不为空
                if (MonsterThing.ThingBehaviour.SkeAni == null)
                {
                    Debug.LogWarning($"怪物 {MonsterThing.Guid} 的SkeAni组件为空，尝试重新获取");
                    MonsterThing.ThingBehaviour.SkeAni = MonsterThing.ThingBehaviour.GetComponentInChildren<SkeletonAnimation>();
                    
                    if (MonsterThing.ThingBehaviour.SkeAni == null)
                    {
                        // 尝试更深层次的查找
                        SkeletonAnimation[] allSkeletonAnimations = MonsterThing.ThingBehaviour.GetComponentsInChildren<SkeletonAnimation>(true);
                        if (allSkeletonAnimations.Length > 0)
                        {
                            MonsterThing.ThingBehaviour.SkeAni = allSkeletonAnimations[0];
                        }
                        else
                        {
                            Debug.LogError($"怪物 {MonsterThing.Guid} 即使搜索非激活对象也无法找到SkeletonAnimation组件，动画播放失败");
                            base.DoShoot(token).Forget();
                            return;
                        }
                    }
                }
                
                // 检查attack01动画是否存在
                bool hasAttackAnimation = false;
                if (MonsterThing.ThingBehaviour.SkeAni != null && 
                    MonsterThing.ThingBehaviour.SkeAni.skeleton != null && 
                    MonsterThing.ThingBehaviour.SkeAni.skeleton.Data != null)
                {
                    hasAttackAnimation = MonsterThing.ThingBehaviour.SkeAni.skeleton.Data.FindAnimation("attack01") != null;
                    
                    if (!hasAttackAnimation)
                    {
                        // 记录所有可用动画
                        var animations = MonsterThing.ThingBehaviour.SkeAni.skeleton.Data.Animations;
                        string availableAnims = animations != null && animations.Count > 0 
                            ? string.Join(", ", animations.Select(a => a.Name)) 
                            : "无动画";
                        Debug.LogWarning($"怪物 {MonsterThing.Guid} 没有attack01动画，可用动画: {availableAnims}");
                    }
                }
                
                // 强制直接设置动画，不通过PlayAnimation方法，避免被其他逻辑覆盖
                if (hasAttackAnimation && MonsterThing.ThingBehaviour.SkeAni != null && MonsterThing.ThingBehaviour.SkeAni.state != null)
                {
                    // 先清空动画队列，避免被其他动画覆盖
                    MonsterThing.ThingBehaviour.SkeAni.state.ClearTrack(0);
                    
                    // 直接使用SetAnimation设置动画，并存储返回的TrackEntry用于后续检查
                    var trackEntry = MonsterThing.ThingBehaviour.SkeAni.state.SetAnimation(0, "attack01", false);
                    
                    if (trackEntry != null)
                    {
                        // 设置动画的优先级为最高，防止被覆盖
                        trackEntry.TimeScale = 1.0f;
                        trackEntry.MixDuration = 0.1f; // 减少混合时间，更快切换到新动画
                        
                        // 设置动画完成后的回调，记录日志
                        trackEntry.Complete += entry => {
                            // 动画完成后，播放idle01动画
                            if (MonsterThing != null && 
                                MonsterThing.ThingBehaviour != null && 
                                MonsterThing.ThingBehaviour.SkeAni != null && 
                                MonsterThing.ThingBehaviour.SkeAni.state != null)
                            {
                                var idleAnimName = "idle01";
                                if (MonsterThing.ThingBehaviour.SkeAni.skeleton.Data.FindAnimation(idleAnimName) != null)
                                {
                                    MonsterThing.ThingBehaviour.SkeAni.state.SetAnimation(0, idleAnimName, true);
                                }
                            }
                        };
                    }
                }
                else if (!hasAttackAnimation)
                {
                    Debug.LogWarning($"没有找到attack01动画，尝试播放idle01或move01");
                    
                    // 如果没有attack01动画，尝试直接设置idle01或move01
                    if (MonsterThing.ThingBehaviour.SkeAni?.skeleton?.Data?.FindAnimation("idle01") != null)
                    {
                        MonsterThing.ThingBehaviour.SkeAni.state.SetAnimation(0, "idle01", true);
                    }
                    else if (MonsterThing.ThingBehaviour.SkeAni?.skeleton?.Data?.FindAnimation("move01") != null)
                    {
                        MonsterThing.ThingBehaviour.SkeAni.state.SetAnimation(0, "move01", true);
                    }
                }
                
                // 等待一小段时间让动画开始播放
                await UniTask.Delay(200, cancellationToken: token);
                
                // 检查动画是否成功播放
                if (MonsterThing.ThingBehaviour.SkeAni != null && 
                    MonsterThing.ThingBehaviour.SkeAni.state != null && 
                    MonsterThing.ThingBehaviour.SkeAni.state.GetCurrent(0) != null)
                {
                    string currentAnim = MonsterThing.ThingBehaviour.SkeAni.state.GetCurrent(0).Animation?.Name;
                    
                    // 如果动画不是attack01，尝试再次强制设置
                    if (hasAttackAnimation && currentAnim != "attack01")
                    {
                        Debug.LogWarning($"检测到动画被覆盖，再次尝试强制设置attack01动画");
                        MonsterThing.ThingBehaviour.SkeAni.state.ClearTrack(0);
                        MonsterThing.ThingBehaviour.SkeAni.state.SetAnimation(0, "attack01", false);
                    }
                }
                else
                {
                    Debug.LogWarning($"怪物 {MonsterThing.Guid} 无法检查当前动画");
                }
            }
            else
            {
                Debug.LogWarning($"怪物不存在: MonsterThing=null");
            }
            
            // 调用基类的DoShoot方法继续发射子弹
            base.DoShoot(token).Forget();
        }

        /// <summary>
        ///     射击一次后(按cd时长的一次循环)
        /// </summary>
        protected override void OnAfterShoot()
        {
            // _ = MonsterThing.AiEventList.Where(x => x.CanSuicide).Select(x =>
            // {
            //     x.ShotTimes.Value++;
            //     
            //     // 怪物自杀(延时)
            //     MessageBroker.Default.Publish(new ThingDead { Thing = MonsterThing });
            //     return x;
            // }).ToList();
        }
    }
}