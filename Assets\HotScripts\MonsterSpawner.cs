// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo
// ReSharper disable CommentTypo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.ChangeBubbling;
using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using CsvTables;

using Cysharp.Threading.Tasks;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using ViewModel;

using X.PB;

/// <summary>
///     刷怪器
/// </summary>
public class MonsterSpawner : IDisposable
{
    /// <summary>
    ///     上次刷怪的时间戳（毫秒）
    /// </summary>
    private static long LastBrushTimeMs = 0;
    
    /// <summary>
    ///     不同刷怪ID之间的最小间隔时间（秒）
    /// </summary>
    private const float MinIntervalBetweenBrushes = 1.0f;

    /// <summary>
    ///     最后一波怪物刷出后自动结束波次的计时器（秒）
    /// </summary>
    private float autoCompleteWaveTimer = 0f;
    
    /// <summary>
    ///     结算检测计时器（秒）
    /// </summary>
    private float settlementCheckTimer = 0f;
    
    /// <summary>
    ///     最后一波怪物是否至少刷出过一只
    /// </summary>
    private bool hasSpawnedLastWaveMonster = false;
    
    /// <summary>
    ///     是否已经自动完成了当前波次
    /// </summary>
    private bool hasAutoCompletedWave = false;

    /// <summary>
    ///     任务的取消令牌:刷怪
    /// </summary>
    public CancellationTokenSource CTS_Brush { get; protected set; } = new();

    /// <summary>
    ///     待刷的怪(队列)
    /// </summary>
    public BubblingList<BornMonster> MonsterQueue { get; } = new(nameof(MonsterQueue));

    /// <summary>
    ///     是否排完了
    /// </summary>
    public BubblingList<bool> IsBrushFinish { get; } = new(nameof(IsBrushFinish));
    
    /// <summary>
    ///     当前刷怪位置索引
    /// </summary>
    private int currentBrushIndex = 0;

    /// <summary>
    ///     每个刷怪ID实际刷出的怪物数量
    /// </summary>
    private Dictionary<int, int> spawnedMonsterCounts = new();

    /// <summary>
    ///     刷怪检测任务的取消令牌
    /// </summary>
    private CancellationTokenSource CTS_CheckMonsters = new();

    /// <summary>
    ///     当前回合的刷怪列表
    /// </summary>
    private List<BattleBrushEnemy.Item> currentRoundBrushList = null;

    /// <summary>
    ///     启动刷怪(一回合)
    /// </summary>
    public async UniTaskVoid StartBrush()
    {
        StopBrush();

        CTS_Brush = new CancellationTokenSource();
        CTS_CheckMonsters = new CancellationTokenSource();

        // 重置标记和计时器
        hasSpawnedLastWaveMonster = false;
        hasAutoCompletedWave = false;
        autoCompleteWaveTimer = 0f;
        settlementCheckTimer = 0f;
        currentBrushIndex = 0;
        LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        CancellationToken token = CTS_Brush.Token;
        Task_BrushMonster(token).Forget();

        // 获取当前回合的刷怪列表
        currentRoundBrushList = SingletonMgr.Instance.GlobalMgr.ListBrushMonster_Stage(
            SingletonMgr.Instance.BattleMgr.StageMgr.CsvRow_Stage.Value)[
            SingletonMgr.Instance.BattleMgr.Actor.RoundNo.Value];

        // 初始化怪物计数字典
        spawnedMonsterCounts.Clear();
        foreach (var item in currentRoundBrushList)
        {
            spawnedMonsterCounts[item.Id] = 0;
        }

        // 启动定期检查怪物数量的任务
        Task_CheckMonstersEveryTwoSeconds(CTS_CheckMonsters.Token).Forget();

        // 先缓存所有待刷怪物的附着属性
        SingletonMgr.Instance.GlobalMgr.GetMonsterPropIds(currentRoundBrushList.ToArray());

        // 当前回合的刷怪配置同时开启子任务
        IEnumerable<UniTask> tasks = currentRoundBrushList.Select(x => Task_BrushOneRow(token, x));
        await UniTask.WhenAll(tasks);
        if (token.IsCancellationRequested)
        {
            return;
        }

        IsBrushFinish.Value = true;
    }

    /// <summary>
    ///     停止刷怪
    /// </summary>
    public void StopBrush()
    {
        try
        {
            if (CTS_Brush != null && !CTS_Brush.IsCancellationRequested)
            {
                CTS_Brush.Cancel();
                CTS_Brush.Dispose();
            }
            
            if (CTS_CheckMonsters != null && !CTS_CheckMonsters.IsCancellationRequested)
            {
                CTS_CheckMonsters.Cancel();
                CTS_CheckMonsters.Dispose();
            }
        }
        catch (Exception)
        {
            // 忽略取消令牌处理过程中的异常
        }
        
        MonsterQueue.Clear();
        spawnedMonsterCounts.Clear();
        currentRoundBrushList = null;
        autoCompleteWaveTimer = 0f;
        settlementCheckTimer = 0f;
        hasSpawnedLastWaveMonster = false;
        hasAutoCompletedWave = false;
    }

    /// <summary>
    ///     出生一个怪物(添加到待刷队列中)
    /// </summary>
    /// <param name="csvRow_Brush">怪物配置</param>
    /// <param name="pos">
    ///     刷怪位置(没传则以配置为准,最多取第一个值)
    ///     (依次替补?)
    /// </param>
    /// <param name="reviveCount">已重生次数</param>
    public void BornMonster(BattleBrushEnemy.Item csvRow_Brush, List<Vector3> pos = null,
        int reviveCount = 0)
    {
        // 更新当前ID的已刷怪物数量
        if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id))
        {
            spawnedMonsterCounts[csvRow_Brush.Id]++;
        }
        else
        {
            spawnedMonsterCounts[csvRow_Brush.Id] = 1;
        }

        BornMonster bornMonster = new()
        {
            CsvRow_Brush = csvRow_Brush,
            MonsterThing = new MonsterThing(nameof(MonsterThing))
            {
                CsvRow_BattleBrushEnemy = csvRow_Brush,
                // 怪物固定为1级
                ThingLvl = { Value = 1 },
                Hp = { Value = csvRow_Brush.Hp },
                Armor = { Value = csvRow_Brush.Armor },
                // PositionInit = pos,
                ReviveCount = reviveCount
            }
        };

        if (pos is { Count: > 0 })
        {
            bornMonster.MonsterThing.Position = bornMonster.MonsterThing.PositionInit = pos.First();
        }

        List<GunItem> gunList = new() { 
    new() { GunId = csvRow_Brush.SkillID, GunLvl = 1, StarCount = -1 },
    new() { GunId = csvRow_Brush.TurnSpeed, GunLvl = 1, StarCount = -1 }
};

        // 加载怪物附着的属性
        bornMonster.MonsterThing.ReloadAttachedProps();

        // 创建枪及附着的属性
        bornMonster.MonsterThing.ReCreateGuns(gunList);

        bornMonster.MonsterThing.ReCalcHoistAndTotal_All();

        // 打印刷怪信息
        //PrintSpawnInfo(csvRow_Brush.Id);
        
        // 如果是最后一波怪物，标记已刷出
        if (currentRoundBrushList != null && 
            currentRoundBrushList.Count > 0 && 
            csvRow_Brush.Id == currentRoundBrushList.Last().Id)
        {
            hasSpawnedLastWaveMonster = true;
        }

        // 加入队列等着出生
        MonsterQueue.Add(bornMonster);
    }

    /// <summary>
    ///     打印刷怪信息
    /// </summary>
    private void PrintSpawnInfo(int currentSpawnId)
    {
        if (currentRoundBrushList == null)
            return;
            
        // 构建单行日志信息
        System.Text.StringBuilder sb = new System.Text.StringBuilder("【刷怪统计】");
        
        // 当前刷出的怪物ID信息
        sb.Append($"当前刷怪ID(本次)={currentSpawnId}, ");
        
        if (spawnedMonsterCounts.ContainsKey(currentSpawnId))
        {
            sb.Append($"ID{currentSpawnId}刷出怪物数量={spawnedMonsterCounts[currentSpawnId]}, ");
            
            // 获取Count值
            int countValue = 0;
            foreach (var item in currentRoundBrushList)
            {
                if (item.Id == currentSpawnId)
                {
                    countValue = item.Count;
                    break;
                }
            }
            
            sb.Append($"ID{currentSpawnId}数量Count={countValue}; ");
        }
        
        // 添加其他正在刷的怪物ID信息
        foreach (var item in currentRoundBrushList)
        {
            if (item.Id == currentSpawnId)
                continue;
                
            if (spawnedMonsterCounts.ContainsKey(item.Id) && spawnedMonsterCounts[item.Id] > 0)
            {
                sb.Append($"当前刷怪ID={item.Id}, ");
                sb.Append($"ID{item.Id}刷出怪物数量={spawnedMonsterCounts[item.Id]}, ");
                sb.Append($"ID{item.Id}数量Count={item.Count}; ");
            }
        }
        
        // 获取当前统计值
        int g = SingletonMgr.Instance.BattleMgr.KillCount.Value;
        int monstersCount = SingletonMgr.Instance.BattleMgr.Monsters.Count;
        int y = 0;
        int k = 0;
        int x = 0;
        
        try
        {
            y = int.Parse(SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s[0]);
            k = int.Parse(SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s[1]);
            x = Mathf.Min((int)(g/(k/(float)y) + 1), y);
        }
        catch (Exception ex)
        {
            //Debug.LogError($"计算波数出错: {ex.Message}");
        }
        
        // 是否最后一波ID
        bool isLastId = IsLastBrushId(currentSpawnId);
        
        // 添加统计值和最后一波信息
        sb.Append($"当前已刷怪物总数量g={g}, 当前场上怪物数量={monstersCount}, X={x}, Y={y}, K={k}, 队列中待刷怪物数量={MonsterQueue.Count}");
        
        // 如果是最后一波怪物，打印额外信息
        if (isLastId)
        {
            sb.Append($", 最后一行刷怪ID={currentSpawnId}, 已经刷出{autoCompleteWaveTimer}秒");
        }
        
        // 使用一个Debug.Log输出完整信息
        //Debug.Log(sb.ToString());
    }

    /// <summary>
    ///     在指定位置附近一定范围内，出生新的怪物
    /// </summary>
    /// <param name="pos">出生位置</param>
    /// <param name="maxDistance">随机范围的最大距离</param>
    /// <param name="brushIds">刷怪Id</param>
    /// <param name="lstBornNum">出生数量</param>
    /// <param name="reviveCount">新怪物的已重生次数</param>
    public void BornMonster(Vector3 pos, float maxDistance, IList<int> brushIds, IList<int> lstBornNum,
        int reviveCount = 0)
    {
        bool isRoundEnd = SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner is
        { IsBrushFinish: { Value: true } } && SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.MonsterQueue.Count == 0;
        if (isRoundEnd) return;
        _ = brushIds.Select((id, i) =>
        {
            if (SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BattleBrushEnemyCsv>().Dic
                .TryGetValue(id, out BattleBrushEnemy.Item csvRow))
            {
                int rebirthCount = lstBornNum.IndexOf_ByCycle(i);
                for (int m = 0; m < rebirthCount; m++)
                {
                    // 重生位置:在 怪物死亡位置，半径在[0,maxDistance)的圆内 随机
                    Vector3 newPos = pos;
                    float distance = RandomNum.RandomFloat(0, maxDistance);
                    float rndAngle = RandomNum.RandomFloat(0, 360);
                    Vector3 dir = Vector3.right.RotateAround(Vector3.forward, rndAngle);
                    newPos += distance * dir;

                    BornMonster(csvRow, new List<Vector3> { newPos, pos }, reviveCount);
                }
            }

            return i;
        }).ToList();
    }

    /// <summary>
    ///     每2秒检查场上怪物数量，在需要时重新刷怪
    /// </summary>
    protected async UniTaskVoid Task_CheckMonstersEveryTwoSeconds(CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(2), cancellationToken: token);
            if (token.IsCancellationRequested)
            {
                break;
            }
            
            try
            {
                // 如果场上没有怪物并且刷怪已完成但还没有触发结算，则直接跳过刷怪检查
                if (SingletonMgr.Instance.BattleMgr.Monsters.Count <= 0 && 
                    IsBrushFinish.Value && MonsterQueue.Count == 0)
                {
                    continue;
                }
                
                // 获取当前UI波数进度
                int x = 0, y = 0;
                GetWaveNumberFromUI(out x, out y);
                
                // 如果X小于Y且场上怪物数量较少，检查是否需要刷新更多怪物
                if (x < y && SingletonMgr.Instance.BattleMgr.Monsters.Count < GetCurrentMonsterCountLimit())
                {
                    // 检查是否当前有足够的怪物已经在队列中等待刷新
                    if (MonsterQueue.Count >= 5)
                    {
                        continue;
                    }
                    
                    // 先检查是否应该刷最后一波的怪物
                    if (currentRoundBrushList != null && currentRoundBrushList.Count > 0)
                    {
                        int lastId = currentRoundBrushList.Last().Id;
                        
                        // 如果应该刷最后一波怪物
                        if (ShouldSpawnLastWaveMonster())
                        {
                            if (spawnedMonsterCounts.ContainsKey(lastId) && 
                                spawnedMonsterCounts[lastId] < currentRoundBrushList.Last().Count)
                            {
                                // 刷新最后一波的怪物
                                await RefreshLastWaveMonster(token);
                            }
                            else
                            {
                                // 最后一波已刷完，但仍需要刷怪，检查其他ID是否还有未刷完的
                                await RefreshUnfinishedWaves(token);
                            }
                        }
                        else
                        {
                            // 如果不应该刷最后一波，先尝试刷未刷够数量的怪
                            bool hasRefreshed = await RefreshUnfinishedWaves(token);
                            
                            // 如果没有找到未刷够的怪，则从第一波开始重新刷
                            if (!hasRefreshed)
                            {
                                await RefreshFromFirstWave(token);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                //Debug.LogError($"Error in Task_CheckMonstersEveryTwoSeconds: {ex.Message}");
            }
        }
    }

    /// <summary>
    ///     刷新未刷够数量的怪物配置
    /// </summary>
    /// <returns>是否找到并刷新了怪物</returns>
    private async UniTask<bool> RefreshUnfinishedWaves(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return false;
            
        // 查找所有未刷够Count的配置
        foreach (var brushItem in currentRoundBrushList)
        {
            if (!spawnedMonsterCounts.ContainsKey(brushItem.Id) || 
                spawnedMonsterCounts[brushItem.Id] < brushItem.Count)
            {
                // 是最后一波且不应该刷最后一波，则跳过
                if (brushItem.Id == currentRoundBrushList.Last().Id && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                // 检查与上次刷怪时间间隔
                long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
                
                if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
                {
                    int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
                    await UniTask.Delay(waitTimeMs, cancellationToken: token);
                    if (token.IsCancellationRequested)
                        return false;
                }
                
                // 获取刷怪位置
                List<Vector2> lstPos = GetSpawnPositions(brushItem);
                
                if (lstPos.Count > 0)
                {
                    Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
                    currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                    List<Vector3> pos = new() { pos2 };
                    
                    // 更新最后刷怪时间
                    LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    
                    // 如果是最后一波怪物，标记已刷出
                    if (brushItem.Id == currentRoundBrushList.Last().Id)
                    {
                        hasSpawnedLastWaveMonster = true;
                    }
                    
                    BornMonster(brushItem, pos);
                    return true;
                }
            }
        }
        
        return false;
    }

    /// <summary>
    ///     从第一波开始重新刷怪（当所有怪都已刷够Count时）
    /// </summary>
    private async UniTask RefreshFromFirstWave(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return;
            
        // 找到第一个可以刷的ID (不是已经刷完的ID)
        int lastScannedIndex = -1;
        BattleBrushEnemy.Item brushItemToSpawn = null;
        
        // 获取最后一个刷怪的ID (从记录最大的ID值)
        int lastSpawnedId = 0;
        foreach (var kvp in spawnedMonsterCounts)
        {
            if (kvp.Value > 0 && kvp.Key > lastSpawnedId)
                lastSpawnedId = kvp.Key;
        }
        
        // 寻找下一个ID (即列表中ID大于lastSpawnedId的第一个ID)
        bool foundNext = false;
        for (int i = 0; i < currentRoundBrushList.Count; i++)
        {
            var brushItem = currentRoundBrushList[i];
            if (brushItem.Id > lastSpawnedId)
            {
                // 是最后一波且不应该刷最后一波，则跳过
                if (brushItem.Id == currentRoundBrushList.Last().Id && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                brushItemToSpawn = brushItem;
                foundNext = true;
                lastScannedIndex = i;
                //Debug.Log($"【刷怪轮换】找到下一个ID={brushItem.Id}，上一个ID={lastSpawnedId}");
                break;
            }
        }
        
        // 如果没有找到下一个ID，说明已经刷到最后一个ID了，从第一个开始重新刷
        if (!foundNext)
        {
            for (int i = 0; i < currentRoundBrushList.Count; i++)
            {
                // 跳过最后一个ID，如果不应该刷
                if (i == currentRoundBrushList.Count - 1 && !ShouldSpawnLastWaveMonster())
                    continue;
                    
                brushItemToSpawn = currentRoundBrushList[i];
                lastScannedIndex = i;
                //Debug.Log($"【刷怪轮换】轮回到第一个ID={brushItemToSpawn.Id}，上一个ID={lastSpawnedId}");
                break;
            }
        }
        
        // 如果找到了需要刷的怪物配置
        if (brushItemToSpawn != null)
        {
            // 检查与上次刷怪时间间隔
            long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
            
            if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
            {
                int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
                await UniTask.Delay(waitTimeMs, cancellationToken: token);
                if (token.IsCancellationRequested)
                    return;
            }
            
            // 获取刷怪位置
            List<Vector2> lstPos = GetSpawnPositions(brushItemToSpawn);
            
            if (lstPos.Count > 0)
            {
                Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
                currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                List<Vector3> pos = new() { pos2 };
                
                // 更新最后刷怪时间
                LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                
                // 如果是最后一波怪物，重置计时器并标记
                if (brushItemToSpawn.Id == currentRoundBrushList.Last().Id)
                {
                    autoCompleteWaveTimer = 0f;
                    hasSpawnedLastWaveMonster = true;
                }
                
                // 生成怪物
                BornMonster(brushItemToSpawn, pos);
                
                // 打印刷怪顺序信息
                //Debug.Log($"【刷怪顺序】开始刷新ID={brushItemToSpawn.Id}，扫描索引={lastScannedIndex}，" + 
                //          $"总波数={currentRoundBrushList.Count}");
            }
        }
    }

    /// <summary>
    ///     刷新最后一波的怪物
    /// </summary>
    private async UniTask RefreshLastWaveMonster(CancellationToken token)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return;
            
        BattleBrushEnemy.Item lastBrush = currentRoundBrushList.Last();
        
        // 检查是否已刷够
        if (spawnedMonsterCounts.ContainsKey(lastBrush.Id) && 
            spawnedMonsterCounts[lastBrush.Id] >= lastBrush.Count)
            return;
            
        // 检查与上次刷怪时间间隔，确保严格按照最小1秒间隔
        long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
        
        if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
        {
            int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
            await UniTask.Delay(waitTimeMs, cancellationToken: token);
            if (token.IsCancellationRequested)
                return;
        }
        
        // 获取刷怪位置
        List<Vector2> lstPos = GetSpawnPositions(lastBrush);
        
        if (lstPos.Count > 0)
        {
            Vector2 pos2 = lstPos[currentBrushIndex % lstPos.Count];
            currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
            List<Vector3> pos = new() { pos2 };
            
            // 更新最后刷怪时间
            LastBrushTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            
            // 标记最后一波怪物已刷出
            hasSpawnedLastWaveMonster = true;
            
            BornMonster(lastBrush, pos);
        }
    }

    /// <summary>
    ///     从第一波开始重新刷怪
    /// </summary>
    private async UniTask RefreshMonsterFromFirstWave(CancellationToken token)
    {
        // 优先使用RefreshUnfinishedWaves查找并刷新未刷完的怪
        bool hasRefreshed = await RefreshUnfinishedWaves(token);
        
        // 如果所有怪都已刷够，则从第一波开始重新刷
        if (!hasRefreshed)
        {
            await RefreshFromFirstWave(token);
        }
    }

    /// <summary>
    ///     获取怪物刷新位置列表
    /// </summary>
    private List<Vector2> GetSpawnPositions(BattleBrushEnemy.Item brushItem)
    {
        List<Vector2> lstPos = new();
        for (int i = 0; i < brushItem.BrushAreas.Length; i += 2)
        {
            if (i % 2 == 0 && i + 1 < brushItem.BrushAreas.Length)
            {
                lstPos.Add(new Vector2(brushItem.BrushAreas[i], brushItem.BrushAreas[i + 1]));
            }
        }
        return lstPos;
    }

    /// <summary>
    ///     延时后刷一波怪
    /// </summary>
    protected async UniTask Task_BrushOneRow(CancellationToken token,
        BattleBrushEnemy.Item csvRow_Brush)
    {
        // 从回合开始起延时刷怪(毫秒)
        int delay = csvRow_Brush.PhysicsDefense;
        await UniTask.Delay(delay, cancellationToken: token);
        if (token.IsCancellationRequested)
        {
            return;
        }

        // 场上怪物数量<=该值时才开始刷怪
        await UniTask.WaitUntil(() =>
                SingletonMgr.Instance.BattleMgr.Monsters.Count <= csvRow_Brush.AntiCriticalStrike,
            cancellationToken: token);
        if (token.IsCancellationRequested)
        {
            return;
        }

        // 如果是最后一波怪并且当前波数UI不满足条件，则等待条件满足
        if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
        {
            await UniTask.WaitUntil(ShouldSpawnLastWaveMonster, cancellationToken: token);
            if (token.IsCancellationRequested)
            {
                return;
            }
        }
        
        // 这波已刷怪数量
        int monsterCount = 0;
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }

            // 检查是否已达到应该刷的数量
            if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count)
            {
                break;
            }

            // 如果是最后一波怪物，检查条件
            if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
            {
                // 如果这波已经至少刷了一只，并且不应该继续刷，则退出
                if (spawnedMonsterCounts[csvRow_Brush.Id] > 0)
                {
                    break;
                }
                
                // 否则等待条件满足
                await UniTask.Delay(500, cancellationToken: token);
                continue;
            }

            // 检查与上次刷怪的时间间隔，确保严格符合最小间隔要求
            long currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            long elapsedTimeMs = currentTimeMs - LastBrushTimeMs;
            
            // 如果距离上次刷怪时间小于设定的间隔，则等待
            if (elapsedTimeMs < MinIntervalBetweenBrushes * 1000)
            {
                int waitTimeMs = (int)(MinIntervalBetweenBrushes * 1000 - elapsedTimeMs);
                await UniTask.Delay(waitTimeMs, cancellationToken: token);
                if (token.IsCancellationRequested)
                {
                    break;
                }
                
                // 重新获取当前时间，确保间隔准确
                currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            }

            // 获取刷怪位置
            List<Vector2> lstPos = GetSpawnPositions(csvRow_Brush);

            // 确保当前索引在刷怪位置列表的有效范围内
            if (lstPos.Count > 0)
            {
                currentBrushIndex = currentBrushIndex % lstPos.Count;
            }
            else
            {
                currentBrushIndex = 0;
            }

            // 波内同时刷多个怪
            for (int i = 0; i < csvRow_Brush.BrushNum; i++)
            {
                // 如果这波已经刷够数量，则退出
                if (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                    spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count)
                {
                    break;
                }

                // 如果是最后一波怪物，再次检查条件
                if (IsLastBrushId(csvRow_Brush.Id) && !ShouldSpawnLastWaveMonster())
                {
                    break;
                }
                
                // 从所有刷怪位置中顺序取一个位置
                if (lstPos.Count > 0 && currentBrushIndex < lstPos.Count)
                {
                    Vector2 pos2 = lstPos[currentBrushIndex];
                    currentBrushIndex = (currentBrushIndex + 1) % lstPos.Count;
                    List<Vector3> pos = new() { pos2 };
                    
                    // 更新最后刷怪时间
                    LastBrushTimeMs = currentTimeMs;
                    
                    BornMonster(csvRow_Brush, pos);
                    monsterCount++;
                    
                    // 每次只刷一个，确保时间间隔
                    break;
                }
                
                // 如果没有配置刷怪位置，则跳出循环
                if (lstPos.Count <= 0)
                {
                    break;
                }
            }

            // 这波刷完了指定数量，退出
            if (monsterCount >= csvRow_Brush.Count || 
                (spawnedMonsterCounts.ContainsKey(csvRow_Brush.Id) && 
                 spawnedMonsterCounts[csvRow_Brush.Id] >= csvRow_Brush.Count))
            {
                break;
            }

            // 波内延时时长(秒)，但确保不小于最小间隔
            float delayTime = Math.Max(float.Parse(csvRow_Brush.DelayTime), MinIntervalBetweenBrushes);
            await UniTask.Delay(TimeSpan.FromSeconds(delayTime), cancellationToken: token);
        }
    }

    /// <summary>
    ///     监视待刷的怪，刷出来
    /// </summary>
    protected async UniTaskVoid Task_BrushMonster(CancellationToken token)
    {
        // 用于控制检查频率的计数器
        int frameCounter = 0;
        const int CHECK_INTERVAL = 10; // 每10帧检查一次
        
        for (;; await UniTask.NextFrame())
        {
            if (token.IsCancellationRequested)
            {
                break;
            }

            if (Time.deltaTime <= 0)
            {
                continue;
            }
            
            // 增加帧计数器
            frameCounter++;
            
            // 降低自动完成波次和结算检测的频率，每CHECK_INTERVAL帧执行一次
            if (frameCounter % CHECK_INTERVAL == 0)
            {
                CheckAutoCompleteWave();
                CheckSettlement();
            }

            BornMonster bornMonster = MonsterQueue.Value;
            if (bornMonster == null)
            {
                continue;
            }

            // 怪物总数限制
            if (SingletonMgr.Instance.BattleMgr.Monsters.Count >= bornMonster.CsvRow_Brush.CountOnScreen)
            {
                continue;
            }
            
            // 刷怪阈值限制：只有当场上怪物数量小于等于AntiCriticalStrike值时才刷新怪物
            if (SingletonMgr.Instance.BattleMgr.Monsters.Count > bornMonster.CsvRow_Brush.AntiCriticalStrike)
            {
                continue;
            }

            // 计算怪物出生点
            bornMonster.MonsterThing.Position = bornMonster.MonsterThing.CalcPositionInit();

            // 通知界面出生此怪物
            MessageBroker.Default.Publish(bornMonster);

            // 从待刷列表中移除
            MonsterQueue.Remove(bornMonster);
            
            // 如果刷出的是最后一波怪物，重置计时器
            if (bornMonster.CsvRow_Brush.Id == currentRoundBrushList?.Last().Id)
            {
                autoCompleteWaveTimer = 0f;
                hasSpawnedLastWaveMonster = true;
            }
        }
    }

    /// <summary>
    ///     检查是否需要自动完成当前波次
    /// </summary>
    private void CheckAutoCompleteWave()
    {
        // 如果已经自动完成了波次或未标记最后一波怪物已刷出，则不执行
        if (hasAutoCompletedWave || !hasSpawnedLastWaveMonster) 
            return;
            
        int x = 0, y = 0;
        GetWaveNumberFromUI(out x, out y);
        
        // 只有在X+1=Y的情况下才考虑自动完成波次
        if (x + 1 == y)
        {
            // 累加计时器
            autoCompleteWaveTimer += Time.deltaTime;
            
            // 如果2秒内没有再刷出怪物，则自动完成波次
            if (autoCompleteWaveTimer >= 2f)
            {
                // 标记波次已自动完成
                hasAutoCompletedWave = true;
                //Debug.Log($"【自动完成波次】当前X={x}, Y={y}, 启用自动完成波次逻辑");
            }
        }
        else
        {
            // 重置计时器
            autoCompleteWaveTimer = 0f;
        }
    }
    
    /// <summary>
    ///     检查是否需要触发结算
    /// </summary>
    private void CheckSettlement()
    {
        // 累加结算检测计时器
        settlementCheckTimer += Time.deltaTime;
        
        // 每秒检查一次
        if (settlementCheckTimer >= 1f)
        {
            settlementCheckTimer = 0f;
            
            int x = 0, y = 0;
            GetWaveNumberFromUI(out x, out y);
            
            // 增加日志打印X和Y的值
        //    Debug.Log($"【结算检查】当前击杀数量X={x}, 目标数量Y={y}, 场上怪物数量={SingletonMgr.Instance.BattleMgr.Monsters.Count}, 队列中待刷怪物数量={MonsterQueue.Count}");
            
            // 根据需求：当X=Y时就立即胜利结束
            if (x >= y && y > 0)
            {
             //   Debug.Log($"【触发结算】击杀数量达到目标 X={x}, Y={y}，立即触发胜利结算");
                SingletonMgr.Instance.BattleMgr.SettleAccounts(true);
                
                // 停止刷怪
                StopBrush();
                return;
            }
        }
    }

    /// <summary>
    ///     是否是回合最后一个刷怪ID
    /// </summary>
    private bool IsLastBrushId(int brushId)
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return false;
            
        return currentRoundBrushList.Last().Id == brushId;
    }

    /// <summary>
    ///     获取当前场上怪物数量限制
    /// </summary>
    private int GetCurrentMonsterCountLimit()
    {
        if (currentRoundBrushList == null || currentRoundBrushList.Count == 0)
            return 0;
            
        // 获取所有配置的最小CountOnScreen值作为限制
        return currentRoundBrushList.Min(b => b.Count);
    }

    /// <summary>
    ///     从UI获取当前波数进度
    /// </summary>
    private void GetWaveNumberFromUI(out int x, out int y)
    {
        x = 0;
        y = 0;
        
        try
        {
            // Y = CatMainStage表Rebirth_item2字段的B值
            y = int.Parse(SingletonMgr.Instance.GlobalMgr.CsvRow_CatMainStage.Rebirthitem2s[1]);
            
            // X = 角色击杀的怪物数量，获取原始值
            int originalX = SingletonMgr.Instance.BattleMgr.KillCount.Value;
            
            // X不能大于Y
            x = Mathf.Min(originalX, y);
            
            // 如果原始值和限制后的值不一致，打印警告日志
            if (originalX != x)
            {
             //   Debug.LogWarning($"【异常检测】原始击杀数量={originalX} 大于目标数量Y={y}，已限制为X={x}");
            }
        }
        catch (Exception ex)
        {
         //   Debug.LogError($"Error getting wave number from UI: {ex.Message}");
        }
    }

    /// <summary>
    ///     是否应该刷最后一波怪物
    /// </summary>
    private bool ShouldSpawnLastWaveMonster()
    {
        int x = 0, y = 0;
        GetWaveNumberFromUI(out x, out y);
        
        // 如果总波数为1，则不做判断，允许刷最后一波
        if (y <= 1)
            return true;
            
        // 如果已经自动完成了波次，允许刷最后一波
        if (hasAutoCompletedWave)
            return true;
            
        // 否则，只有当当前波数x+1等于总波数y时，才刷最后一波
        return x + 1 >= y;
    }

    #region IDisposable

    protected bool disposedValue;

    /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
    protected virtual void Dispose(bool disposing)
    {
        if (disposedValue)
        {
            return;
        }

        if (disposing)
        {
            StopBrush();
            
            // 清理资源集合
            MonsterQueue?.Clear();
            IsBrushFinish?.Clear();
            spawnedMonsterCounts?.Clear();
            currentRoundBrushList = null;
        }

        // 释放未托管的资源(未托管的对象)并重写终结器
        // 将大型字段设置为 null
        disposedValue = true;
    }

    // // TODO: 仅当"Dispose(bool disposing)"拥有用于释放未托管资源的代码时才替代终结器
    // ~NotifyPropertyChangeBase()
    // {
    //     // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
    //     Dispose(false);
    // }

    public void Dispose()
    {
        // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    #endregion
}