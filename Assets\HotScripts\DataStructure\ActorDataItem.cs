﻿// ReSharper disable InconsistentNaming

using System;

using Apq.ChangeBubbling;
using Apq.Props;

using Cysharp.Threading.Tasks;

using DTO.ActorDataDTO;

using Newtonsoft.Json;

using View;

#if UNITY_WEBGL
using WeChatWASM;
#endif

namespace DataStructure
{
    /// <summary>
    /// 一个角色数据
    /// </summary>
    public class ActorDataItem : PropValue
    {
        public ActorDataItem(object key = null, IBubbleNode parent = null)
            : base(key, parent)
        {
        }

        /// <summary>
        /// 数据类别
        /// </summary>
        public int DataCatalog { get; set; }

        /// <summary>
        /// 值的类型{0:删除,1:整数,2:浮点数,3:字符串}
        /// </summary>
        public byte ValueType { get; set; }
        
        /// <summary>
        /// 数据的说明
        /// </summary>
        public string Remark { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 数据是否已保存至服务器
        /// </summary>
        public bool IsSaveToServer { get; set; }

        /// <summary>
        /// 转为本地存储对象
        /// </summary>
        public ActorDataListItemDTO ToLocalStorageItemDTO()
        {
            var rtn = new ActorDataListItemDTO
            {
                DataCatalog = DataCatalog,
                ValueType = ValueType,
                IsTimeScope = InvalidMode == 1,
                StartTime = StartTime,
                EndTime = EndTime,
                UpdateTime = UpdateTime ?? SingletonMgr.Instance.GlobalMgr.GetServerNow(),
                //Remark = Remark,	// 本地不存说明
            };

            rtn.LongValues.AddRange(LongValues);
            rtn.DoubleValues.AddRange(DoubleValues);
            rtn.StrValues.AddRange(StrValues);

            return rtn;
        }

        /// <summary>
        /// 如果本地存储的值 “更” 新，则替换为本地存储的值
        /// </summary>
        /// <returns>是否替换过值</returns>
        public bool LoadFromLocalStorage(long ActorId)
        {
            var json = UnityEngine.PlayerPrefs.GetString($"LocalActorData_{ActorId}_{DataCatalog}");
#if UNITY_WEBGL
            var fs = WXBase.GetFileSystemManager();
            var file = $"{WXBase.env.USER_DATA_PATH}/LocalActorData_{ActorId}/{DataCatalog}.json";
            if (!"access:ok".Equals(fs.AccessSync(file))) return false;

            json = fs.ReadFileSync(file, "utf8");
#endif
            var dto = JsonConvert.DeserializeObject<ActorDataListItemDTO>(json);
            if (dto == null || (UpdateTime ?? DateTime.MinValue) >= dto.UpdateTime) return false;

            //DataCatalog = dto.DataCatalog;	// 这个不会变
            ValueType = dto.ValueType;
            InvalidMode = dto.IsTimeScope ? 1 : 0;
            StartTime = dto.StartTime;
            EndTime = dto.EndTime;
            //Remark = dto.Remark;	// 本地没存说明
            UpdateTime = dto.UpdateTime;

            LongValues.Clear();
            LongValues.AddRange(dto.LongValues);
            DoubleValues.Clear();
            DoubleValues.AddRange(dto.DoubleValues);
            StrValues.Clear();
            StrValues.AddRange(dto.StrValues);

            return true;
        }

        /// <summary>
        /// 保存到本地存储
        /// </summary>
        /// <remarks>ValueType == 0 时就是删除</remarks>
        public async UniTaskVoid SaveToLocalStorage(long ActorId)
        {
            var json = JsonConvert.SerializeObject(ToLocalStorageItemDTO());
            await UniTask.SwitchToMainThread();

#if !UNITY_WEBGL
            var playerPrefKey = $"LocalActorData_{ActorId}_{DataCatalog}";

            if (ValueType == 0)
            {
                UnityEngine.PlayerPrefs.DeleteKey(playerPrefKey);
            }
            else
            {
                UnityEngine.PlayerPrefs.SetString(playerPrefKey, json);
            }
#else
            var fs = WXBase.GetFileSystemManager();
            var folder = $"{WXBase.env.USER_DATA_PATH}/LocalActorData_{ActorId}";
            if (!"access:ok".Equals(fs.AccessSync(folder)))
            {
                fs.MkdirSync(folder, true);
            }

            var file = $"{folder}/{DataCatalog}.json";

            if (ValueType == 0)
            {
                fs.RemoveSavedFile(new()
                {
                    filePath = file
                });
            }
            else
            {
                fs.WriteFileSync(file, json);
            }
#endif
        }

        /// <summary>
        /// 从传输对象加载数据
        /// </summary>
        public ActorDataItem LoadFromActorDataListItemDTO(ActorDataListItemDTO dto)
        {
            DataCatalog = dto.DataCatalog;
            ValueType = dto.ValueType;
            InvalidMode = dto.IsTimeScope ? 1 : 0;
            StartTime = dto.StartTime;
            EndTime = dto.EndTime;
            Remark = dto.Remark;

            LongValues.Clear();
            LongValues.AddRange(dto.LongValues);
            DoubleValues.Clear();
            DoubleValues.AddRange(dto.DoubleValues);
            StrValues.Clear();
            StrValues.AddRange(dto.StrValues);

            return this;
        }

        /// <summary>
        /// 转为传输对象
        /// </summary>
        public ActorDataListItemDTO ToActorDataListItemDTO()
        {
            var rtn = new ActorDataListItemDTO
            {
                DataCatalog = DataCatalog,
                ValueType = ValueType,
                IsTimeScope = InvalidMode == 1,
                StartTime = StartTime,
                EndTime = EndTime,
                Remark = Remark,
            };

            rtn.LongValues.AddRange(LongValues);
            rtn.DoubleValues.AddRange(DoubleValues);
            rtn.StrValues.AddRange(StrValues);

            return rtn;
        }
    }
}